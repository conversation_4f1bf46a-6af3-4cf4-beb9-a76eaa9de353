/*++

Copyright (c) Microsoft Corporation. All rights reserved.

Module Name:

    Program.cs

Abstract:

    Main entry point for the NetRag Service.
    Configures RAG services, vector stores, and PowerShell command search.

Author:

    <PERSON><PERSON><PERSON> (rizhang) 07/29/2025

Environment:

    User mode only.

--*/

using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Microsoft.SemanticKernel;
using System.Runtime.InteropServices;
using System.Runtime.Versioning;
using AimxShared.Constants;

namespace NetRagService;

class Program
{
    static async Task Main(string[] args)
    {
        // Log startup to Event Log for debugging
        if (OperatingSystem.IsWindows())
        {
            LogToEventLog("NetRag service starting with args: " + string.Join(" ", args), System.Diagnostics.EventLogEntryType.Information);
        }

        // Check if running as Windows service or console application
        // Service mode: no arguments or --service argument, or when not running interactively
        bool isWindowsService = RuntimeInformation.IsOSPlatform(OSPlatform.Windows) &&
                               (args.Length == 0 || args.Contains("--service") || !Environment.UserInteractive);

        if (isWindowsService)
        {
            if (OperatingSystem.IsWindows())
            {
                LogToEventLog("Running as Windows service", System.Diagnostics.EventLogEntryType.Information);
                await RunAsWindowsService(args);
            }
            else
            {
                throw new PlatformNotSupportedException("Windows service mode is only supported on Windows.");
            }
        }
        else
        {
            if (OperatingSystem.IsWindows())
            {
                LogToEventLog("Running as console application", System.Diagnostics.EventLogEntryType.Information);
            }
            await RunAsConsoleApplication(args);
        }
    }

    [SupportedOSPlatform("windows")]
    static async Task RunAsWindowsService(string[] args)
    {
        LogToEventLog("RunAsWindowsService called", System.Diagnostics.EventLogEntryType.Information);

        var builder = Host.CreateDefaultBuilder(args);

        LogToEventLog("Configuring as Windows service", System.Diagnostics.EventLogEntryType.Information);

        // Configure as Windows service
        builder.UseWindowsService(options =>
        {
            options.ServiceName = "NetRagService";
        });

        LogToEventLog("Configuring registry-based configuration", System.Diagnostics.EventLogEntryType.Information);

        // Build configuration - registry only
        builder.ConfigureAppConfiguration((context, config) =>
        {
            config.AddAimxServiceRegistry();
        });

        LogToEventLog("Configuring services", System.Diagnostics.EventLogEntryType.Information);

        // Configure services
        builder.ConfigureServices((context, services) =>
        {
            LogToEventLog("Binding configuration from registry", System.Diagnostics.EventLogEntryType.Information);

            // Bind configuration
            var ragConfig = new RagConfiguration();
            context.Configuration.Bind(ragConfig);
            services.AddSingleton(ragConfig);

            LogToEventLog("Registering NetRagWindowsService", System.Diagnostics.EventLogEntryType.Information);

            // Register the Windows service
            services.AddHostedService<NetRagWindowsService>();
        });

        // Configure logging for Windows service
        builder.ConfigureLogging((context, logging) =>
        {
            logging.ClearProviders();
            logging.AddEventLog(options =>
            {
                options.SourceName = "NetRag Service";
                options.LogName = "Application";
            });
            logging.SetMinimumLevel(LogLevel.Information);
        });

        LogToEventLog("Building host", System.Diagnostics.EventLogEntryType.Information);

        var host = builder.Build();

        LogToEventLog("Starting host", System.Diagnostics.EventLogEntryType.Information);

        await host.RunAsync();

        LogToEventLog("Host completed", System.Diagnostics.EventLogEntryType.Information);
    }

    /// <summary>
    /// Configure services for both console and Windows service modes
    /// </summary>
    /// <param name="builder">Web application builder</param>
    /// <param name="ragConfig">RAG configuration</param>
    /// <param name="isConsoleMode">True if running in console mode, false for Windows service</param>
    public static void ConfigureServices(WebApplicationBuilder builder, RagConfiguration ragConfig, bool isConsoleMode = false)
    {
        // Register configuration objects
        builder.Services.AddSingleton(ragConfig);
        builder.Services.AddSingleton(ragConfig.FoundryLocal);
        builder.Services.AddSingleton(ragConfig.Embedding);
        builder.Services.AddSingleton(ragConfig.DocumentIngestion);
        builder.Services.AddSingleton(ragConfig.InMemoryVectorStore);
        builder.Services.AddSingleton(ragConfig.McpService);

        // Configure Semantic Kernel
        var kernelBuilder = Kernel.CreateBuilder();

        // Add BERT ONNX embedding generator
        kernelBuilder.AddBertOnnxEmbeddingGenerator(
            ragConfig.Embedding.ModelPath,
            ragConfig.Embedding.VocabPath);

        // Add OpenAI chat completion (Foundry Local)
        kernelBuilder.AddOpenAIChatCompletion(
            ragConfig.FoundryLocal.ModelName,
            new Uri(ragConfig.FoundryLocal.BaseUrl),
            apiKey: "",
            serviceId: ragConfig.FoundryLocal.ServiceId);

        var kernel = kernelBuilder.Build();
        builder.Services.AddSingleton(kernel);

        // Get services from kernel
        var embeddingService = kernel.GetRequiredService<Microsoft.Extensions.AI.IEmbeddingGenerator<string, Microsoft.Extensions.AI.Embedding<float>>>();
        var chatService = kernel.GetRequiredService<Microsoft.SemanticKernel.ChatCompletion.IChatCompletionService>(serviceKey: ragConfig.FoundryLocal.ServiceId);

        builder.Services.AddSingleton(embeddingService);
        builder.Services.AddSingleton(chatService);

        // Register application services with SqliteVec support (following DatabaseConverter pattern)
        builder.Services.AddSingleton<VectorStoreService>(provider =>
        {
            var logger = provider.GetRequiredService<ILogger<VectorStoreService>>();
            var persistenceFilePath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, ragConfig.InMemoryVectorStore.PersistenceFilePath);

            // Create VectorStoreService with SqliteVec support
            var vectorStore = new VectorStoreService(logger, persistenceFilePath);

            // Set the embedding generator for SqliteVec functionality
            vectorStore.SetEmbeddingGenerator(embeddingService);

            return vectorStore;
        });
        builder.Services.AddSingleton<DocumentIngestionService>();
        builder.Services.AddSingleton<RagQueryService>();
        builder.Services.AddSingleton<McpToolService>();

        // Register PowerShell command services
        builder.Services.AddSingleton<PowerShellCommandSearchService>();

        // Register PowerShell execution service
        builder.Services.AddSingleton<PowerShellExecutionService>();

       
        // Add controllers and API services
        builder.Services.AddControllers();

        // Configure CORS
        builder.Services.AddCors(options =>
        {
            options.AddDefaultPolicy(policy =>
            {
                policy.AllowAnyOrigin()
                      .AllowAnyMethod()
                      .AllowAnyHeader();
            });
        });

        // Configure logging based on mode
        builder.Logging.ClearProviders();
        if (isConsoleMode)
        {
            builder.Logging.AddConsole();
        }
        else if (OperatingSystem.IsWindows())
        {
            builder.Logging.AddEventLog(); // Log to Windows Event Log for service mode
        }
        builder.Logging.SetMinimumLevel(LogLevel.Information);
    }

    /// <summary>
    /// Configure the HTTP request pipeline for both console and Windows service modes
    /// </summary>
    /// <param name="app">Web application</param>
    /// <param name="ragConfig">RAG configuration</param>
    public static void ConfigureRequestPipeline(WebApplication app, RagConfiguration ragConfig)
    {
        // Configure CORS if enabled
        if (ragConfig.McpService.EnableCors)
        {
            app.UseCors();
        }

        app.UseRouting();
        app.MapControllers();
    }

    /// <summary>
    /// Initialize services for both console and Windows service modes
    /// </summary>
    /// <param name="app">Web application</param>
    /// <param name="ragConfig">RAG configuration</param>
    /// <param name="logger">Logger for initialization messages</param>
    public static async Task InitializeServices(WebApplication app, RagConfiguration ragConfig, ILogger? logger = null)
    {
        // Initialize vector store (SqliteVec will automatically load existing data)
        var vectorStoreService = app.Services.GetRequiredService<VectorStoreService>();
        await vectorStoreService.InitializeAsync(ragConfig.Embedding.VectorSize);

        // Note: SqliteVec automatically loads existing data during initialization
        // No need to call LoadFromDiskAsync() for SqliteVec databases

        var vectorCount = await vectorStoreService.GetVectorCountAsync();

        if (logger != null)
        {
            if (vectorCount == -1)
            {
                logger.LogInformation("Vector store initialized with vector size: {VectorSize}. Using SqliteVec database",
                    ragConfig.Embedding.VectorSize);
            }
            else
            {
                logger.LogInformation("Vector store initialized with vector size: {VectorSize}. Found {VectorCount} vectors in database",
                    ragConfig.Embedding.VectorSize, vectorCount);
            }
        }
        else
        {
            if (vectorCount == -1)
            {
                Console.WriteLine("Using SqliteVec database");
            }
            else
            {
                Console.WriteLine($"Found {vectorCount} vectors in database");
            }
        }
    }

    static async Task RunAsConsoleApplication(string[] args)
    {
        var builder = WebApplication.CreateBuilder(args);

        // Build configuration - registry only for consistency with Windows system services
        builder.Configuration.AddAimxServiceRegistry();

        // Bind configuration (registry values will automatically override appsettings.json)
        var ragConfig = new RagConfiguration();
        builder.Configuration.Bind(ragConfig);

        // Configure all services using shared method
        ConfigureServices(builder, ragConfig, isConsoleMode: true);

        var app = builder.Build();

        // Configure the HTTP request pipeline using shared method
        ConfigureRequestPipeline(app, ragConfig);

        // Initialize services using shared method
        await InitializeServices(app, ragConfig);

        Console.WriteLine("MCP Tools RAG Service starting...");
        Console.WriteLine($"Service will be available at: http://{ragConfig.McpService.Host}:{ragConfig.McpService.Port}");
        Console.WriteLine("Press Ctrl+C to stop the service");

        await app.RunAsync();
    }

    /// <summary>
    /// Helper method to log messages to Windows Event Log
    /// </summary>
    /// <param name="message">Message to log</param>
    /// <param name="entryType">Type of log entry</param>
    [SupportedOSPlatform("windows")]
    private static void LogToEventLog(string message, System.Diagnostics.EventLogEntryType entryType)
    {
        try
        {
            using var eventLog = new System.Diagnostics.EventLog("Application");
            eventLog.Source = "NetRag Service";
            eventLog.WriteEntry(message, entryType);
        }
        catch
        {
            // If we can't log to event log, there's not much we can do
            // This prevents infinite recursion if logging itself fails
        }
    }
}
