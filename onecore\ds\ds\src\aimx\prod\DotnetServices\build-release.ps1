#!/usr/bin/env pwsh
#
# Copyright (c) Microsoft Corporation. All rights reserved.
# Author: <PERSON><PERSON><PERSON> (rizhang)
# Date: 07/29/2025
#
# Simple AIMX Release Builder - Binaries Only

param(
    [string]$Configuration = "Release",
    [string]$OutputPath = ".\Release",
    [switch]$Clean = $false
)

$ErrorActionPreference = "Stop"

Write-Host "AIMX Release Builder - Binaries Only" -ForegroundColor Cyan
Write-Host "Configuration: $Configuration" -ForegroundColor Green
Write-Host "Output: $OutputPath" -ForegroundColor Green
Write-Host ""

# Clean previous release if requested
if ($Clean -and (Test-Path $OutputPath)) {
    Write-Host "Cleaning previous release..." -ForegroundColor Yellow
    Remove-Item -Path $OutputPath -Recurse -Force
}

# Create consolidated release directory
Write-Host "Creating consolidated release structure..." -ForegroundColor Yellow
New-Item -Path $OutputPath -ItemType Directory -Force | Out-Null

# Build all projects
Write-Host "Building solution..." -ForegroundColor Yellow
try {
    if ($Clean) {
        dotnet clean DotnetServices.sln -c $Configuration --verbosity quiet
    }

    dotnet build DotnetServices.sln -c $Configuration --verbosity quiet

    if ($LASTEXITCODE -ne 0) {
        throw "Build failed"
    }

    Write-Host "Build completed" -ForegroundColor Green
} catch {
    Write-Host "Build failed: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

# Copy binaries to consolidated folder with shared dependencies
Write-Host "Copying binaries to consolidated folder..." -ForegroundColor Yellow

# Track copied files to avoid duplicates
$copiedFiles = @{}

# Function to copy files and directories, avoiding duplicates for shared dependencies
function Copy-ConsolidatedFiles {
    param($SourcePath, $ServiceName)

    if (-not (Test-Path $SourcePath)) {
        Write-Host "  $ServiceName binaries not found" -ForegroundColor Yellow
        return 0, 0
    }

    $serviceFiles = 0
    $sharedFiles = 0

    # Copy all files in the root directory
    $files = Get-ChildItem -Path $SourcePath -File
    foreach ($file in $files) {
        $destFile = Join-Path $OutputPath $file.Name

        # Always copy service-specific executables and configs
        if ($file.Name -like "*.exe" -or $file.Name -like "*$ServiceName*" -or $file.Name -like "appsettings*.json") {
            Copy-Item -Path $file.FullName -Destination $destFile -Force
            $serviceFiles++
        }
        # For shared dependencies, only copy if not already present
        elseif (-not $copiedFiles.ContainsKey($file.Name)) {
            Copy-Item -Path $file.FullName -Destination $destFile -Force
            $copiedFiles[$file.Name] = $true
            $sharedFiles++
        }
    }

    # Copy all subdirectories (localization folders, runtimes, etc.)
    $directories = Get-ChildItem -Path $SourcePath -Directory
    foreach ($dir in $directories) {
        $destDir = Join-Path $OutputPath $dir.Name

        # Only copy directory if not already present (avoid duplicates)
        if (-not $copiedFiles.ContainsKey($dir.Name)) {
            if (Test-Path $destDir) {
                # If directory exists, merge contents
                Copy-Item -Path "$($dir.FullName)\*" -Destination $destDir -Recurse -Force
            } else {
                # Copy entire directory
                Copy-Item -Path $dir.FullName -Destination $destDir -Recurse -Force
            }
            $copiedFiles[$dir.Name] = $true
            $sharedFiles++
        }
    }

    return $serviceFiles, $sharedFiles
}

# Copy all services to the same consolidated folder
$intentFiles = Copy-ConsolidatedFiles "IntentPlanningService\bin\$Configuration\net8.0\win-x64" "IntentPlanningService"
Write-Host "  IntentPlanningService: $($intentFiles[0]) service files, $($intentFiles[1]) shared deps" -ForegroundColor Green

$ragFiles = Copy-ConsolidatedFiles "NetRagService\bin\$Configuration\net8.0\win-x64" "NetRagService"
Write-Host "  NetRagService: $($ragFiles[0]) service files, $($ragFiles[1]) shared deps" -ForegroundColor Green

$dbFiles = Copy-ConsolidatedFiles "DatabaseConverter\bin\$Configuration\net8.0\win-x64" "DatabaseConverter"
Write-Host "  DatabaseConverter: $($dbFiles[0]) service files, $($dbFiles[1]) shared deps" -ForegroundColor Green

# Copy shared libraries
$sharedSource = "Shared\bin\$Configuration\net8.0"
if (Test-Path $sharedSource) {
    Copy-Item -Path "$sharedSource\*" -Destination $OutputPath -Force
    Write-Host "  Shared libraries copied" -ForegroundColor Green
}

# Ensure data directory exists in release
$dataDir = Join-Path $OutputPath "data"
if (-not (Test-Path $dataDir)) {
    New-Item -ItemType Directory -Path $dataDir -Force | Out-Null
    Write-Host "  Created data directory" -ForegroundColor Green
}

# Create simple README
$buildDate = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
$readme = @"
# AIMX Consolidated Release

Build Date: $buildDate
Configuration: $Configuration

## Consolidated Deployment

All services and tools are in this single folder with shared dependencies.

## Available Executables

- IntentPlanningService.exe - Intent planning service (Port 5000)
- NetRagService.exe - RAG service (Port 8082)
- DatabaseConverter.exe - Database conversion tool

## Data Folder Structure

The `data/` subfolder contains all model and database files:
- data/model.onnx - ONNX embedding model
- data/vocab.txt - Vocabulary file for tokenization
- data/database.bin - Vector database file
- data/comprehensive_ad_commands_dataset_final.json - Command dataset

## Usage

All executables can be run directly from this folder:

IntentPlanningService.exe
NetRagService.exe
DatabaseConverter.exe

## Setup Instructions

1. Place your model files in the data/ folder:
   - Copy model.onnx to data/model.onnx
   - Copy vocab.txt to data/vocab.txt
   - Copy database.bin to data/database.bin (if available)

2. Run the services directly from this folder

## Dependencies

All required DLLs and dependencies are consolidated in this folder.
Dependencies are shared between services to minimize disk usage.
No additional installation required.
"@

$readme | Out-File -FilePath "$OutputPath\README.txt" -Encoding UTF8

Write-Host ""
Write-Host "RELEASE COMPLETED!" -ForegroundColor Green
Write-Host "Location: $((Resolve-Path $OutputPath).Path)" -ForegroundColor Cyan
Write-Host ""

# Show what was created
if (Test-Path $OutputPath) {
    Write-Host "Consolidated Release Contents:" -ForegroundColor Yellow

    # Count executables
    $exeFiles = Get-ChildItem -Path $OutputPath -Filter "*.exe" -File
    Write-Host "  Executables: $($exeFiles.Count)" -ForegroundColor White
    foreach ($exe in $exeFiles) {
        Write-Host "    - $($exe.Name)" -ForegroundColor Gray
    }

    # Count total files
    $totalFiles = (Get-ChildItem -Path $OutputPath -File -Recurse).Count
    Write-Host "  Total files: $totalFiles (dependencies shared)" -ForegroundColor White
}

Write-Host ""
Write-Host "Ready for deployment!" -ForegroundColor Green
