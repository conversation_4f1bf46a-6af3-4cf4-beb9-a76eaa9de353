{"_comment": "Configuration values should match constants defined in AimxShared.Constants.AimxConstants for consistency across services", "Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning", "Microsoft.SemanticKernel": "Information", "IntentPlanningService": "Debug"}}, "AllowedHosts": "*", "Service": {"Host": "localhost", "Port": 8082, "ServiceName": "IntentPlanningService", "EnableCors": true, "EnableSwagger": true, "RequestTimeoutSeconds": 30, "MaxConcurrentRequests": 100}, "Slm": {"ModelPath": "models/intent-model.onnx", "VocabPath": "models/vocab.txt", "MaxTokens": 512, "Temperature": 0.1, "VectorSize": 384, "EnableCaching": true, "CacheExpirationMinutes": 60, "ConfidenceThreshold": 0.7}, "ExternalServices": {"NetRagService": {"BaseUrl": "http://localhost:5000", "TimeoutSeconds": 30, "MaxRetries": 3, "RetryDelaySeconds": 2, "EnableCircuitBreaker": true, "CircuitBreakerFailureThreshold": 5, "CircuitBreakerTimeoutSeconds": 60, "Headers": {}}, "LlmService": {"BaseUrl": "http://*************:5273", "TimeoutSeconds": 30, "MaxRetries": 2, "RetryDelaySeconds": 2, "EnableCircuitBreaker": true, "CircuitBreakerFailureThreshold": 3, "CircuitBreakerTimeoutSeconds": 30, "Headers": {}}, "WorkflowEngine": {"BaseUrl": "http://localhost:8084", "TimeoutSeconds": 60, "MaxRetries": 1, "RetryDelaySeconds": 5, "EnableCircuitBreaker": true, "CircuitBreakerFailureThreshold": 2, "CircuitBreakerTimeoutSeconds": 120, "Headers": {}}, "AimxServer": {"BaseUrl": "http://localhost:8080", "TimeoutSeconds": 30, "MaxRetries": 2, "RetryDelaySeconds": 3, "EnableCircuitBreaker": true, "CircuitBreakerFailureThreshold": 3, "CircuitBreakerTimeoutSeconds": 60, "Headers": {}}}, "IntentAnalysis": {"MinConfidenceThreshold": 0.6, "EnableContextEnrichment": true, "MaxContextItems": 10}, "WorkflowPlanning": {"MinPlanningConfidence": 0.7, "MaxWorkflowSteps": 20, "DefaultStepTimeoutSeconds": 300, "MaxRetryAttempts": 3, "EnableParallelExecution": true, "EnableRollbackPlanning": true, "EnableAlternativeWorkflows": true, "MaxAlternativeWorkflows": 3}, "Security": {"EnableRiskAssessment": true, "RequireApprovalForHighRisk": true, "RequireApprovalForCriticalRisk": true, "AutoApprovedOperations": ["get_user", "list_users", "get_group", "list_groups", "get_computer", "list_computers", "search_ad"], "AlwaysRequireApproval": ["delete_user", "delete_computer", "modify_domain_admin", "change_domain_policy"], "PermissionRequirements": {"user_management": ["AD_USER_ADMIN"], "group_management": ["AD_GROUP_ADMIN"], "computer_management": ["AD_COMPUTER_ADMIN"], "security_operations": ["AD_SECURITY_ADMIN"], "system_administration": ["AD_SYSTEM_ADMIN"]}}}