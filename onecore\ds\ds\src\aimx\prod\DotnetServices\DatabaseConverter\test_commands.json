[{"CommandName": "Get-Process", "Synopsis": "Gets the processes that are running on the local computer", "Description": "The Get-Process cmdlet gets the processes on a local or remote computer", "Module": "Microsoft.PowerShell.Management", "Parameters": [{"Name": "Name", "Description": "Specifies one or more process names", "Type": "String[]", "Mandatory": false}], "Examples": [{"Title": "Example 1", "Description": "Get all processes", "Code": "Get-Process"}]}]