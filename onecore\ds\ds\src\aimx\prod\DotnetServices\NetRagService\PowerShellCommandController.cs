/*++

Copyright (c) Microsoft Corporation. All rights reserved.

Module Name:

    PowerShellCommandController.cs

Abstract:

    REST API controller for PowerShell command search and retrieval.
    Provides endpoints for searching and discovering PowerShell commands.

Author:

    <PERSON><PERSON><PERSON> (rizhang) 07/29/2025

Environment:

    User mode only.

--*/

using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using AimxShared.Constants;

namespace NetRagService;

[ApiController]
[Route("api/[controller]")]
public class PowerShellCommandController : ControllerBase
{
    private readonly PowerShellCommandSearchService _searchService;
    private readonly ILogger<PowerShellCommandController> _logger;

    public PowerShellCommandController(
        PowerShellCommandSearchService searchService,
        ILogger<PowerShellCommandController> logger)
    {
        _searchService = searchService;
        _logger = logger;
    }

    /// <summary>
    /// Search for PowerShell commands using semantic similarity
    /// </summary>
    /// <param name="query">The search query</param>
    /// <param name="limit">Maximum number of results to return (default: 5, max: 20)</param>
    /// <returns>List of matching PowerShell commands</returns>
    [HttpGet("search")]
    public async Task<ActionResult<List<PowerShellCommandResult>>> Search(
        [FromQuery] string query,
        [FromQuery] int limit = AimxConstants.SearchLimits.DefaultSearchLimit)
    {
        if (string.IsNullOrWhiteSpace(query))
        {
            return BadRequest("Query parameter is required");
        }

        if (limit <= 0 || limit > AimxConstants.SearchLimits.MaxSearchLimit)
        {
            return BadRequest($"Limit must be between 1 and {AimxConstants.SearchLimits.MaxSearchLimit}");
        }

        try
        {
            var results = await _searchService.SearchAsync(query, limit);
            return Ok(results);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to search PowerShell commands");
            return StatusCode(500, "Internal server error occurred while searching");
        }
    }


}
