/*++

Copyright (c) Microsoft Corporation. All rights reserved.

Module Name:

    WorkflowPlanningService.cs

Abstract:

    Service for generating executable workflows from user goals.
    Implements enhanced PowerShell command generation with rich context and reasoning.

Author:

    <PERSON><PERSON><PERSON> (rizhang) 07/29/2025

Environment:

    User mode only.

--*/

using IntentPlanningService.Configuration;
using IntentPlanningService.Models;
using System.Text.Json;
using System.Text.Json.Serialization;
using System.Collections.Concurrent;
using Microsoft.Extensions.Logging;
using AimxShared.Constants;

namespace IntentPlanningService.Services;

/// <summary>
/// Data models for PowerShell comprehensive dataset
/// </summary>
public class ComprehensiveDatasetCommand
{
    public string CommandName { get; set; } = string.Empty;
    public List<string> Syntax { get; set; } = new();
    public List<ParameterInfo> Parameters { get; set; } = new();
    public List<ExampleInfo> Examples { get; set; } = new();
    public string Description { get; set; } = string.Empty;
    public string Synopsis { get; set; } = string.Empty;
    public List<string> Inputs { get; set; } = new();
    public List<string> Outputs { get; set; } = new();
    public List<string> Notes { get; set; } = new();
    public List<RelatedLinkInfo> RelatedLinks { get; set; } = new();
}

public class ParameterInfo
{
    public string Name { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public ParameterDetails Details { get; set; } = new();
}

public class ParameterDetails
{
    public string AcceptPipelineInput { get; set; } = string.Empty;
    public string Required { get; set; } = string.Empty;
    public string AcceptWildcardCharacters { get; set; } = string.Empty;
    public string Type { get; set; } = string.Empty;
    public string Aliases { get; set; } = string.Empty;
    public string Default { get; set; } = string.Empty;
    public string Position { get; set; } = string.Empty;
}

public class ExampleInfo
{
    public string Title { get; set; } = string.Empty;
    public string Code { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
}

public class RelatedLinkInfo
{
    public string Text { get; set; } = string.Empty;
    public string Uri { get; set; } = string.Empty;
}

public class RagSearchResult
{
    public string Id { get; set; } = string.Empty;
    public string CommandName { get; set; } = string.Empty;
    public double Score { get; set; }
    public int ParameterCount { get; set; }
    public int ExampleCount { get; set; }
    public string ParameterNames { get; set; } = string.Empty;
}

/// <summary>
/// Service for generating executable workflows from user goals
/// </summary>
public class WorkflowPlanningService
{
    private readonly ILogger<WorkflowPlanningService> _logger;
    private readonly IntentPlanningConfiguration _config;
    private readonly ToolDiscoveryService _toolDiscovery;
    private readonly RiskAssessmentService _riskAssessment;
    private readonly IHttpClientFactory _httpClientFactory;
    private readonly SemaphoreSlim _semaphore;

    // COMMAND CONTEXT HASHMAP - Loaded once at startup for rich LLM context
    private static readonly ConcurrentDictionary<string, ComprehensiveDatasetCommand> _commandContextCache = new();
    private static bool _commandContextLoaded = false;
    private static readonly object _loadLock = new object();

    public WorkflowPlanningService(
        ILogger<WorkflowPlanningService> logger,
        IntentPlanningConfiguration config,
        ToolDiscoveryService toolDiscovery,
        RiskAssessmentService riskAssessment,
        IHttpClientFactory httpClientFactory)
    {
        _logger = logger;
        _config = config;
        _toolDiscovery = toolDiscovery;
        _riskAssessment = riskAssessment;
        _httpClientFactory = httpClientFactory;
        _semaphore = new SemaphoreSlim(_config.Service.MaxConcurrentRequests, _config.Service.MaxConcurrentRequests);

        // Load command context cache on first instantiation
        EnsureCommandContextLoaded();
    }

    /// <summary>
    /// Ensure command context cache is loaded from comprehensive dataset
    /// </summary>
    private void EnsureCommandContextLoaded()
    {
        if (_commandContextLoaded) return;

        lock (_loadLock)
        {
            if (_commandContextLoaded) return;

            try
            {
                var datasetPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, AimxConstants.FilePaths.ComprehensiveDatasetPath);

                if (!File.Exists(datasetPath))
                {
                    _logger.LogError("Command context dataset not found: {DatasetPath}", datasetPath);
                    return;
                }

                _logger.LogInformation("Loading command context cache from comprehensive dataset...");
                var jsonContent = File.ReadAllText(datasetPath);
                var commands = JsonSerializer.Deserialize<List<ComprehensiveDatasetCommand>>(jsonContent, new JsonSerializerOptions
                {
                    PropertyNameCaseInsensitive = true
                });

                if (commands != null)
                {
                    foreach (var command in commands)
                    {
                        if (!string.IsNullOrEmpty(command.CommandName))
                        {
                            _commandContextCache.TryAdd(command.CommandName.ToLowerInvariant(), command);
                        }
                    }

                    _logger.LogInformation("Loaded {Count} commands into context cache", _commandContextCache.Count);
                    _commandContextLoaded = true;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to load command context cache");
            }
        }
    }

    /// <summary>
    /// Get rich command context from cache for LLM enhancement
    /// </summary>
    private ComprehensiveDatasetCommand? GetCommandContext(string commandName)
    {
        if (string.IsNullOrEmpty(commandName)) return null;

        _commandContextCache.TryGetValue(commandName.ToLowerInvariant(), out var context);
        return context;
    }

    /// <summary>
    /// Generate an executable workflow from a user goal using new LLM-driven approach
    /// </summary>
    public async Task<ExecutableWorkflow> GenerateWorkflowAsync(UserGoal userGoal, List<AvailableTool> availableTools)
    {
        await _semaphore.WaitAsync();
        try
        {
            _logger.LogInformation("=== STARTING NEW LLM-DRIVEN WORKFLOW GENERATION ===");
            _logger.LogInformation("Goal ID: {GoalId}", userGoal.GoalId);
            _logger.LogInformation("Primary Objective: '{Objective}'", userGoal.PrimaryObjective);
            _logger.LogInformation("Success Criteria: '{Criteria}'", userGoal.SuccessCriteria);

            var stopwatch = System.Diagnostics.Stopwatch.StartNew();

            // REDESIGNED: Check if this is a direct command operation
            var intentCategory = userGoal.Context.GetValueOrDefault("intentCategory", "");
            if (intentCategory == "direct_command")
            {
                _logger.LogInformation(" DIRECT COMMAND detected - using optimized single-step workflow");
                return await GenerateDirectCommandWorkflowAsync(userGoal, availableTools);
            }

            // NEW FLOW: Step 1 - Ask LLM for step breakdown
            _logger.LogInformation("STEP 1: Getting step breakdown from LLM");
            var stepDescriptions = await GetWorkflowStepsFromLlmAsync(userGoal.PrimaryObjective);
            _logger.LogInformation("LLM identified {StepCount} steps for goal", stepDescriptions.Count);

            if (!stepDescriptions.Any())
            {
                _logger.LogWarning("LLM could not break down goal into steps: {Goal}", userGoal.PrimaryObjective);
                return CreateErrorWorkflow(userGoal, "Could not break down goal into actionable steps");
            }

            foreach (var (step, index) in stepDescriptions.Select((s, i) => (s, i)))
            {
                _logger.LogInformation("  - Step {Number}: {Description}", index + 1, step);
            }

            // NEW FLOW: Step 2 - For each step, find command and generate parameters
            _logger.LogInformation("STEP 2: Processing each step through RAG → Dataset → LLM");
            var workflowSteps = new List<WorkflowStep>();

            for (int i = 0; i < stepDescriptions.Count; i++)
            {
                var stepDescription = stepDescriptions[i];
                _logger.LogInformation("Processing step {StepNumber}: {Description}", i + 1, stepDescription);

                try
                {
                    // Search RAG for best command
                    var commandName = await SearchRagForCommandAsync(stepDescription);
                    if (string.IsNullOrEmpty(commandName))
                    {
                        _logger.LogWarning("No command found for step: {StepDescription}", stepDescription);
                        continue;
                    }

                    _logger.LogInformation("Found command {CommandName} for step: {StepDescription}", commandName, stepDescription);

                    // Get full command details from dataset
                    _logger.LogInformation("CALLING GetCommandDetailsFromDatasetAsync for: {CommandName}", commandName);
                    var commandDetails = await GetCommandDetailsFromDatasetAsync(commandName);
                    _logger.LogInformation("Dataset lookup result: {IsNull}", commandDetails == null ? "NULL" : "FOUND");
                    if (commandDetails == null)
                    {
                        _logger.LogWarning("Command details not found in dataset for: {CommandName}", commandName);
                        continue;
                    }

                    // Generate final command with parameters using ENHANCED LLM with reasoning framework
                    var finalCommand = await GenerateFinalCommandAsync(stepDescription, commandDetails);
                    if (string.IsNullOrEmpty(finalCommand))
                    {
                        _logger.LogWarning("Could not generate final command for step: {StepDescription}", stepDescription);
                        continue;
                    }

                    _logger.LogInformation("Generated final command: {FinalCommand}", finalCommand);

                    // Create workflow step
                    var workflowStep = new WorkflowStep
                    {
                        StepId = Guid.NewGuid().ToString(),
                        StepName = $"Step {i + 1}",
                        Description = stepDescription,
                        Operation = finalCommand,
                        ToolId = $"ps_{commandName.ToLowerInvariant().Replace("-", "")}",
                        Parameters = new Dictionary<string, object>
                        {
                            { "command", finalCommand },
                            { "description", stepDescription },
                            { "commandName", commandName },
                            { "stepNumber", i + 1 },
                            { "requiresApproval", DetermineIfApprovalRequired(finalCommand) }
                        },
                        TimeoutSeconds = 120, // 2 minutes default
                        Dependencies = new List<string>()
                    };

                    workflowSteps.Add(workflowStep);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error processing step {StepNumber}: {StepDescription}", i + 1, stepDescription);
                }
            }

            _logger.LogInformation("Generated {StepCount} executable workflow steps", workflowSteps.Count);

            if (!workflowSteps.Any())
            {
                _logger.LogWarning("No executable workflow steps could be generated");
                return CreateErrorWorkflow(userGoal, "No executable steps could be generated from the goal");
            }

            // Step 3: Create the executable workflow
            var workflow = CreateExecutableWorkflow(userGoal, workflowSteps);

            // Step 4: Perform risk assessment
            var riskAssessment = await _riskAssessment.AssessWorkflowRiskAsync(workflow);
            workflow.RiskLevel = riskAssessment.RiskLevel;
            workflow.ApprovalRequired = riskAssessment.RequiresApproval;
            workflow.ApprovalReason = riskAssessment.ApprovalReason;

            // Step 5: Set final values
            workflow.PlanningConfidence = 0.9; // High confidence with new LLM-driven approach
            workflow.EstimatedTotalTimeSeconds = workflowSteps.Sum(s => s.TimeoutSeconds);

            stopwatch.Stop();
            _logger.LogInformation("NEW WORKFLOW generation completed for goal {GoalId} in {ElapsedMs}ms with {StepCount} steps",
                userGoal.GoalId, stopwatch.ElapsedMilliseconds, workflowSteps.Count);

            return workflow;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error generating workflow for goal {GoalId}", userGoal.GoalId);
            return CreateErrorWorkflow(userGoal, ex.Message);
        }
        finally
        {
            _semaphore.Release();
        }
    }

    /// <summary>
    /// Generate alternative workflows for the same goal
    /// </summary>
    public Task<List<ExecutableWorkflow>> GenerateAlternativeWorkflowsAsync(UserGoal userGoal, List<AvailableTool> availableTools)
    {
        // Alternative workflows completely disabled - user feedback: "completely not needed"
        return Task.FromResult(new List<ExecutableWorkflow>());
    }

    #region Private Methods

    /// <summary>
    /// Analyze the user goal to determine required operations using AI
    /// </summary>
    private async Task<List<RequiredOperation>> AnalyzeRequiredOperationsAsync(UserGoal userGoal)
    {
        try
        {
            var intentCategory = userGoal.Context.GetValueOrDefault("intentCategory", "system_administration")?.ToString() ?? "system_administration";

            var prompt = $@"You are an IT operations expert. Break down this goal into specific actionable operations.

Goal: {userGoal.PrimaryObjective}
Category: {intentCategory}
Success Criteria: {userGoal.SuccessCriteria}

Common operations by category:
- user_management: create_user, modify_user, delete_user, reset_password, unlock_account, enable_user, disable_user
- group_management: create_group, add_user_to_group, remove_user_from_group, modify_group, delete_group
- computer_management: join_domain, move_computer, reset_computer, enable_computer, disable_computer
- security_operations: set_permissions, audit_access, configure_security
- system_administration: configure_system, manage_services, update_settings
- troubleshooting: diagnose_issue, check_status, analyze_logs, test_connectivity

Extract 1-3 specific operations needed to achieve this goal.
Respond with only operation names separated by commas (e.g., create_user, add_user_to_group).";

            var response = await CallLlmAsync(prompt);
            var operationNames = response.Trim().Split(',').Select(op => op.Trim()).Where(op => !string.IsNullOrEmpty(op)).ToList();

            var operations = new List<RequiredOperation>();
            for (int i = 0; i < operationNames.Count; i++)
            {
                operations.Add(new RequiredOperation
                {
                    OperationType = operationNames[i],
                    Category = intentCategory,
                    Description = $"{operationNames[i]} for: {userGoal.PrimaryObjective}",
                    Priority = i + 1
                });
            }

            _logger.LogInformation("Extracted {Count} operations: {Operations}",
                operations.Count, string.Join(", ", operations.Select(op => op.OperationType)));

            return operations;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in AI operation analysis, falling back to generic operation");

            // Fallback to generic operation
            var intentCategory = userGoal.Context.GetValueOrDefault("intentCategory", "system_administration")?.ToString() ?? "system_administration";
            return new List<RequiredOperation>
            {
                new RequiredOperation
                {
                    OperationType = "execute",
                    Category = intentCategory,
                    Description = userGoal.PrimaryObjective,
                    Priority = 1
                }
            };
        }
    }

    /// <summary>
    /// Map required operations to available tools
    /// </summary>
    private Task<List<WorkflowStep>> MapOperationsToToolsAsync(List<RequiredOperation> operations, List<AvailableTool> availableTools)
    {
        var steps = new List<WorkflowStep>();
        int executionOrder = 1;

        foreach (var operation in operations.OrderBy(op => op.Priority))
        {
            _logger.LogInformation("  Mapping operation: {Type} in category {Category}",
                operation.OperationType, operation.Category);

            // Find suitable tools for this operation
            var suitableTools = FindSuitableTools(operation, availableTools);
            _logger.LogInformation("    Found {Count} suitable tools", suitableTools.Count);

            if (!suitableTools.Any())
            {
                _logger.LogWarning("    No suitable tools found for operation {OperationType} in category {Category}",
                    operation.OperationType, operation.Category);

                // Debug: Show what tools are available in this category
                var categoryTools = availableTools.Where(t => t.Category.Equals(operation.Category, StringComparison.OrdinalIgnoreCase)).ToList();
                _logger.LogInformation("    Available tools in category '{Category}': {Count}", operation.Category, categoryTools.Count);
                foreach (var tool in categoryTools.Take(3))
                {
                    _logger.LogInformation("      - {ToolName}: {Operations}", tool.ToolName, string.Join(", ", tool.Operations.Select(o => o.OperationName)));
                }
                continue;
            }

            // Select the best tool based on matching quality
            var selectedTool = SelectBestMatchingTool(suitableTools, operation.OperationType);
            if (selectedTool == null)
            {
                _logger.LogWarning("No suitable tool found for operation: {OperationType}", operation.OperationType);
                continue;
            }

            var selectedOperation = selectedTool.Operations.FirstOrDefault(op =>
                IsOperationMatch(op, operation.OperationType));

            if (selectedOperation == null)
            {
                _logger.LogWarning("No matching operation found in tool {ToolId} for {OperationType}", 
                    selectedTool.ToolId, operation.OperationType);
                continue;
            }

            // Create workflow step
            var step = new WorkflowStep
            {
                StepName = $"{operation.OperationType}_{operation.Category}",
                Description = operation.Description,
                ToolId = selectedTool.ToolId,
                Operation = selectedOperation.OperationId,
                TimeoutSeconds = selectedOperation.EstimatedDurationSeconds + 30, // Add buffer
                ExecutionOrder = executionOrder++,
                RetryPolicy = new RetryPolicy
                {
                    MaxRetries = _config.WorkflowPlanning.MaxRetryAttempts,
                    RetryDelaySeconds = 5,
                    ExponentialBackoff = true
                }
            };

            // Set risk level based on operation (AI-driven risk assessment to be implemented)
            var riskLevel = operation.OperationType.Contains("delete") || operation.OperationType.Contains("remove") ? "high" : "medium";
            
            // Add required permissions
            step.Parameters["requiredPermissions"] = selectedOperation.RequiredPermissions;
            step.Parameters["riskLevel"] = riskLevel;
            step.Parameters["operationType"] = operation.OperationType;

            steps.Add(step);
        }

        return Task.FromResult(steps);
    }

    /// <summary>
    /// Find tools suitable for a given operation
    /// </summary>
    private List<AvailableTool> FindSuitableTools(RequiredOperation operation, List<AvailableTool> availableTools)
    {
        return availableTools.Where(tool =>
            tool.IsAvailable &&
            (tool.Category == operation.Category || tool.Category == "general") &&
            tool.Operations.Any(op => IsOperationMatch(op, operation.OperationType))
        ).ToList();
    }

    /// <summary>
    /// Check if a tool operation matches the required operation type
    /// </summary>
    private bool IsOperationMatch(ToolOperation toolOperation, string requiredOperationType)
    {
        var operationName = toolOperation.OperationName.ToLowerInvariant().Replace("-", "_");
        var operationType = requiredOperationType.ToLowerInvariant();

        _logger.LogDebug("Matching operation: '{RequiredOperation}' against tool operation: '{ToolOperation}'",
            operationType, operationName);

        // Direct match
        if (operationName.Contains(operationType))
        {
            _logger.LogDebug("Direct match found");
            return true;
        }

        // Extract verb from operation type (e.g., "create_user" -> "create")
        var verb = operationType.Split('_')[0];
        var toolVerb = operationName.Split('_')[0];

        // Enhanced synonym matching with context awareness
        var synonyms = new Dictionary<string, string[]>
        {
            { "create", new[] { "add", "new", "insert", "make" } },
            { "add", new[] { "create", "new", "insert", "make" } },
            { "delete", new[] { "remove", "del", "destroy" } },
            { "remove", new[] { "delete", "del", "destroy" } },
            { "update", new[] { "modify", "change", "edit", "set" } },
            { "modify", new[] { "update", "change", "edit", "set" } },
            { "query", new[] { "search", "find", "get", "list", "retrieve" } },
            { "get", new[] { "query", "search", "find", "list", "retrieve" } },
            { "enable", new[] { "activate", "turn_on", "start" } },
            { "disable", new[] { "deactivate", "turn_off", "stop" } },
            { "reset", new[] { "clear", "restore", "refresh" } },
            { "join", new[] { "connect", "attach", "link" } },
            { "move", new[] { "transfer", "relocate", "migrate" } }
        };

        // Check verb synonyms
        if (synonyms.TryGetValue(verb, out var operationSynonyms))
        {
            if (operationSynonyms.Contains(toolVerb))
            {
                _logger.LogDebug("Verb synonym match: '{RequiredVerb}' matches '{ToolVerb}'", verb, toolVerb);

                // Additional context matching for complex operations
                if (IsContextualMatch(operationType, operationName))
                {
                    _logger.LogDebug("Contextual match confirmed");
                    return true;
                }
            }
        }

        // Check reverse mapping (tool verb -> operation verb)
        if (synonyms.TryGetValue(toolVerb, out var toolSynonyms))
        {
            if (toolSynonyms.Contains(verb))
            {
                _logger.LogDebug("Reverse verb synonym match: '{ToolVerb}' matches '{RequiredVerb}'", toolVerb, verb);

                if (IsContextualMatch(operationType, operationName))
                {
                    _logger.LogDebug("Contextual match confirmed");
                    return true;
                }
            }
        }

        _logger.LogDebug("No match found");
        return false;
    }

    /// <summary>
    /// NEW FLOW: Ask LLM to break down user goal into step descriptions
    /// </summary>
    private async Task<List<string>> GetWorkflowStepsFromLlmAsync(string userGoal)
    {
        try
        {
            var prompt = $@"Break down this IT task into the minimum necessary steps. Respond with TASK DESCRIPTIONS only, NOT PowerShell commands.

Task: {userGoal}

Rules:
- Provide 1-2 task descriptions maximum
- Each step describes WHAT needs to be done (not HOW)
- Do NOT include PowerShell syntax or commands
- Focus on the essential actions only

Examples:

Task: ""Create user John Smith in Sales group""
1. Create new Active Directory user account for John Smith
2. Add user John Smith to the Sales security group

Task: ""Reset password for user Mary""
1. Reset Active Directory account password for user Mary

Task: ""Add user Bob to Finance group""
1. Add user Bob to the Finance security group

Task: ""Disable user account for John""
1. Disable Active Directory user account for John

Now break down this task: {userGoal}";

            var response = await CallLlmAsync(prompt);

            // Check if LLM service failed and returned fallback
            if (response == "fallback_operation")
            {
                _logger.LogWarning("LLM service failed, using rule-based fallback for step generation");
                // Generate a basic step based on the user goal
                var fallbackStep = GenerateFallbackStep(userGoal);
                _logger.LogInformation("Generated fallback step: {Step}", fallbackStep);
                return new List<string> { fallbackStep };
            }

            // Parse the numbered list response
            var steps = new List<string>();
            var lines = response.Split('\n', StringSplitOptions.RemoveEmptyEntries);

            foreach (var line in lines)
            {
                var trimmed = line.Trim();
                if (System.Text.RegularExpressions.Regex.IsMatch(trimmed, @"^\d+\.\s*(.+)"))
                {
                    var match = System.Text.RegularExpressions.Regex.Match(trimmed, @"^\d+\.\s*(.+)");
                    if (match.Success)
                    {
                        steps.Add(match.Groups[1].Value.Trim());
                    }
                }
            }

            // If no steps were parsed, generate a fallback
            if (steps.Count == 0)
            {
                _logger.LogWarning("No steps parsed from LLM response, using fallback");
                var fallbackStep = GenerateFallbackStep(userGoal);
                steps.Add(fallbackStep);
            }

            _logger.LogInformation("Parsed {StepCount} steps from LLM response", steps.Count);
            return steps;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting workflow steps from LLM");
            return new List<string>();
        }
    }

    /// <summary>
    /// NEW FLOW: Search RAG database for best PowerShell command for a step
    /// </summary>
    private async Task<string?> SearchRagForCommandAsync(string stepDescription)
    {
        try
        {
            using var httpClient = _httpClientFactory.CreateClient();
            httpClient.Timeout = TimeSpan.FromSeconds(AimxConstants.Timeouts.ShortHttpTimeoutSeconds);

            // Get TOP 3 results from RAG (not just 1!)
            var encodedQuery = Uri.EscapeDataString(stepDescription);
            var searchUrl = $"{AimxConstants.ServiceEndpoints.NetRagServiceBaseUrl}{AimxConstants.ApiEndpoints.PowerShellCommandSearch}?query={encodedQuery}&limit={AimxConstants.SearchLimits.RagSearchTopResults}";

            _logger.LogInformation("Searching RAG for TOP 3 commands: {SearchUrl}", searchUrl);

            var response = await httpClient.GetAsync(searchUrl);

            if (!response.IsSuccessStatusCode)
            {
                _logger.LogWarning("RAG search failed with status: {StatusCode}", response.StatusCode);
                return null;
            }

            var responseJson = await response.Content.ReadAsStringAsync();
            var searchResults = System.Text.Json.JsonSerializer.Deserialize<List<RagSearchResult>>(responseJson, new JsonSerializerOptions { PropertyNameCaseInsensitive = true });

            if (searchResults?.Any() != true)
            {
                _logger.LogWarning("No RAG results found for step: {StepDescription}", stepDescription);
                return null;
            }

            // Log the TOP 3 options
            _logger.LogInformation("RAG returned {Count} commands for step: {StepDescription}", searchResults.Count, stepDescription);
            for (int i = 0; i < searchResults.Count; i++)
            {
                _logger.LogInformation("  {Index}. {CommandName} (Score: {Score:F4})", i + 1, searchResults[i].CommandName, searchResults[i].Score);
            }

            // Ask LLM to pick the best command from TOP 3 options
            var bestCommand = await AskLlmToPickBestCommandAsync(stepDescription, searchResults);

            _logger.LogInformation("LLM selected command: {CommandName} for step: {StepDescription}", bestCommand, stepDescription);
            return bestCommand;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error searching RAG for step: {StepDescription}", stepDescription);
            return null;
        }
    }

    /// <summary>
    /// Ask LLM to pick the best PowerShell command from TOP 3 RAG results
    /// </summary>
    private async Task<string?> AskLlmToPickBestCommandAsync(string stepDescription, List<RagSearchResult> top3Commands)
    {
        try
        {
            // Build prompt with the 3 options
            var optionsText = string.Join("\n", top3Commands.Select((cmd, i) =>
                $"{i + 1}. {cmd.CommandName} (Score: {cmd.Score:F4})"));

            var combinedPrompt = $"You are a PowerShell expert. Pick the best command for the task.\n\nTask: {stepDescription}\n\nAvailable commands:\n{optionsText}\n\nRespond with only the command name (e.g., 'Set-ADAccountPassword'):";

            var llmResponse = await CallLlmAsync(combinedPrompt);

            // Extract command name from response
            var selectedCommand = llmResponse?.Trim();

            // Validate that the selected command is one of the options
            var validCommand = top3Commands.FirstOrDefault(cmd =>
                selectedCommand?.Contains(cmd.CommandName, StringComparison.OrdinalIgnoreCase) == true);

            if (validCommand != null)
            {
                _logger.LogInformation("LLM selected: {SelectedCommand} from {OptionCount} options", validCommand.CommandName, top3Commands.Count);
                return validCommand.CommandName;
            }

            // HOTFIX: If LLM suggests a better command that's not in RAG options, use it if it's valid
            if (!string.IsNullOrEmpty(selectedCommand))
            {
                var correctedCommand = TryCorrectCommandFromLlmResponse(selectedCommand, stepDescription);
                if (!string.IsNullOrEmpty(correctedCommand))
                {
                    _logger.LogWarning("CORRECTED: Using LLM suggestion '{LlmCommand}' instead of RAG result '{RagCommand}' for step: {StepDescription}",
                        correctedCommand, top3Commands[0].CommandName, stepDescription);
                    return correctedCommand;
                }
            }

            // Fallback to first option if LLM response is invalid
            _logger.LogWarning("LLM response '{Response}' not recognized, using top RAG result: {FallbackCommand}", selectedCommand, top3Commands[0].CommandName);
            return top3Commands[0].CommandName;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error asking LLM to pick best command, using top RAG result");
            return top3Commands[0].CommandName;
        }
    }

    /// <summary>
    /// Try to extract and validate a PowerShell command from LLM response
    /// </summary>
    private string? TryCorrectCommandFromLlmResponse(string llmResponse, string stepDescription)
    {
        try
        {
            // Extract command name from LLM response (e.g., "Get-ADUser -Identity John Doe" -> "Get-ADUser")
            var commandMatch = System.Text.RegularExpressions.Regex.Match(llmResponse, @"^([A-Za-z]+-[A-Za-z]+)");
            if (!commandMatch.Success)
                return null;

            var extractedCommand = commandMatch.Groups[1].Value;

            // Validate against common AD commands that should be trusted over RAG
            var trustedCommands = new HashSet<string>(StringComparer.OrdinalIgnoreCase)
            {
                "Get-ADUser", "Set-ADUser", "New-ADUser", "Remove-ADUser", "Enable-ADAccount", "Disable-ADAccount",
                "Get-ADGroup", "Set-ADGroup", "New-ADGroup", "Remove-ADGroup", "Add-ADGroupMember", "Remove-ADGroupMember",
                "Get-ADComputer", "Set-ADComputer", "New-ADComputer", "Remove-ADComputer",
                "Set-ADAccountPassword", "Unlock-ADAccount", "Search-ADAccount",
                "Get-ADOrganizationalUnit", "Set-ADOrganizationalUnit", "New-ADOrganizationalUnit"
            };

            if (trustedCommands.Contains(extractedCommand))
            {
                _logger.LogInformation("Validated LLM command: {Command} for step: {StepDescription}", extractedCommand, stepDescription);
                return extractedCommand;
            }

            return null;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error extracting command from LLM response: {Response}", llmResponse);
            return null;
        }
    }

    /// <summary>
    /// Generate optimized workflow for direct PowerShell command operations
    /// </summary>
    private async Task<ExecutableWorkflow> GenerateDirectCommandWorkflowAsync(UserGoal userGoal, List<AvailableTool> availableTools)
    {
        try
        {
            var stopwatch = System.Diagnostics.Stopwatch.StartNew();

            // Extract suggested command from context
            var suggestedCommand = userGoal.Context.GetValueOrDefault("suggestedCommand", "");
            _logger.LogInformation(" Processing direct command: {Command}", suggestedCommand);

            // If we have a suggested command, use it directly
            string? commandName = suggestedCommand;
            if (string.IsNullOrEmpty(commandName))
            {
                // Fallback: Try to identify command from the objective
                commandName = await TryIdentifyCommandFromObjectiveAsync(userGoal.PrimaryObjective);
            }

            if (string.IsNullOrEmpty(commandName))
            {
                _logger.LogWarning("Could not identify PowerShell command for direct operation: {Objective}", userGoal.PrimaryObjective);
                return CreateErrorWorkflow(userGoal, "Could not identify PowerShell command for this operation");
            }

            _logger.LogInformation("Identified command: {CommandName}", commandName);

            // Get command details from dataset
            var commandDetails = await GetCommandDetailsFromDatasetAsync(commandName);
            if (commandDetails == null)
            {
                _logger.LogWarning("Command details not found in dataset for: {CommandName}", commandName);
                return CreateErrorWorkflow(userGoal, $"Command details not available for {commandName}");
            }

            // Generate the final PowerShell command with parameters
            var finalCommand = await GenerateFinalCommandAsync(userGoal.PrimaryObjective, commandDetails);
            if (string.IsNullOrEmpty(finalCommand))
            {
                _logger.LogWarning("Could not generate final command for: {Objective}", userGoal.PrimaryObjective);
                return CreateErrorWorkflow(userGoal, "Could not generate PowerShell command");
            }

            _logger.LogInformation("Generated direct command: {FinalCommand}", finalCommand);

            // Create single-step workflow
            var workflowStep = new WorkflowStep
            {
                StepId = Guid.NewGuid().ToString(),
                StepName = "Execute PowerShell Command",
                Description = userGoal.PrimaryObjective,
                Operation = finalCommand,
                ToolId = $"ps_{commandName.ToLowerInvariant().Replace("-", "")}",
                Parameters = new Dictionary<string, object>
                {
                    { "command", finalCommand },
                    { "description", userGoal.PrimaryObjective },
                    { "commandName", commandName },
                    { "timeout", 120 },
                    { "maxRetries", 3 }
                }
            };

            stopwatch.Stop();

            var workflow = new ExecutableWorkflow
            {
                WorkflowId = Guid.NewGuid().ToString(),
                WorkflowName = $"Direct Command: {commandName}",
                Description = $"Execute {commandName} for: {userGoal.PrimaryObjective}",
                Steps = new List<WorkflowStep> { workflowStep },
                CreatedBy = userGoal.Context.GetValueOrDefault("userId", "system"),
                PlanningMethod = "direct_command_optimized",
                PlanningConfidence = 0.95
            };

            _logger.LogInformation("DIRECT COMMAND workflow generated in {ElapsedMs}ms with 1 step", stopwatch.ElapsedMilliseconds);
            return workflow;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error generating direct command workflow");
            return CreateErrorWorkflow(userGoal, "Error generating direct command workflow");
        }
    }

    /// <summary>
    /// Try to identify PowerShell command from objective text
    /// </summary>
    private async Task<string?> TryIdentifyCommandFromObjectiveAsync(string objective)
    {
        try
        {
            var prompt = $@"Extract the PowerShell cmdlet name from this objective:

Objective: ""{objective}""

EXAMPLES:
- ""Execute Get-ADDefaultDomainPasswordPolicy to retrieve domain password policy"" → Get-ADDefaultDomainPasswordPolicy
- ""Execute Get-ADUser to display user account information"" → Get-ADUser
- ""Execute Get-ADGroup to list group information"" → Get-ADGroup

Extract only the PowerShell cmdlet name (e.g., 'Get-ADUser'), or respond 'NONE' if no cmdlet is mentioned.

Response:";

            var response = await CallLlmAsync(prompt);
            var result = response.Trim();

            if (result.StartsWith("Get-AD") || result.StartsWith("Set-AD") || result.StartsWith("New-AD") ||
                result.StartsWith("Remove-AD") || result.StartsWith("Add-AD") || result.StartsWith("Search-AD"))
            {
                return result;
            }

            return null;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error identifying command from objective");
            return null;
        }
    }

    /// <summary>
    /// Select the best matching tool based on operation specificity and quality
    /// </summary>
    private AvailableTool? SelectBestMatchingTool(List<AvailableTool> suitableTools, string operationType)
    {
        if (!suitableTools.Any())
            return null;

        // Score each tool based on matching quality
        var scoredTools = suitableTools.Select(tool => new
        {
            Tool = tool,
            Score = CalculateMatchingScore(tool, operationType)
        }).OrderByDescending(x => x.Score).ToList();

        _logger.LogDebug("Tool matching scores for operation '{OperationType}':", operationType);
        foreach (var scored in scoredTools.Take(3))
        {
            _logger.LogDebug("  - {ToolId}: {Score:F2}", scored.Tool.ToolId, scored.Score);
        }

        // Return the highest scoring healthy tool, or the highest scoring tool if none are healthy
        var bestTool = scoredTools.FirstOrDefault(x => x.Tool.HealthStatus == "healthy")?.Tool
                      ?? scoredTools.First().Tool;

        _logger.LogDebug("Selected tool: {ToolId} for operation: {OperationType}", bestTool.ToolId, operationType);
        return bestTool;
    }

    /// <summary>
    /// Calculate matching score for a tool against an operation type
    /// </summary>
    private double CalculateMatchingScore(AvailableTool tool, string operationType)
    {
        double score = 0.0;
        var normalizedOperation = operationType.ToLowerInvariant();
        var normalizedToolId = tool.ToolId.ToLowerInvariant().Replace("-", "_");

        // Exact verb match gets highest score
        var operationVerb = normalizedOperation.Split('_')[0];
        var toolVerb = normalizedToolId.Split('_')[1]; // Skip "ps_" prefix

        if (operationVerb == toolVerb)
        {
            score += 10.0; // Exact verb match
        }
        else if (IsVerbSynonym(operationVerb, toolVerb))
        {
            score += 7.0; // Synonym match
        }

        // Context word matching
        var operationWords = normalizedOperation.Split('_');
        var toolWords = normalizedToolId.Split('_');

        foreach (var opWord in operationWords.Skip(1)) // Skip verb
        {
            foreach (var toolWord in toolWords.Skip(2)) // Skip "ps_" and verb
            {
                if (opWord == toolWord)
                {
                    score += 5.0; // Exact context match
                }
                else if (IsContextSynonym(opWord, toolWord))
                {
                    score += 3.0; // Context synonym match
                }
            }
        }

        // Specificity bonus - prefer more specific operations
        if (normalizedOperation.Contains("_to_") && normalizedToolId.Contains("member"))
        {
            score += 2.0; // Bonus for membership operations
        }

        return score;
    }

    /// <summary>
    /// Check if two verbs are synonyms
    /// </summary>
    private bool IsVerbSynonym(string verb1, string verb2)
    {
        var synonymGroups = new[]
        {
            new[] { "add", "create", "new", "insert", "make" },
            new[] { "remove", "delete", "del", "destroy" },
            new[] { "update", "modify", "change", "edit", "set" },
            new[] { "get", "query", "search", "find", "list", "retrieve" },
            new[] { "enable", "activate", "turn_on", "start" },
            new[] { "disable", "deactivate", "turn_off", "stop" }
        };

        return synonymGroups.Any(group => group.Contains(verb1) && group.Contains(verb2));
    }

    /// <summary>
    /// Check if two context words are synonyms
    /// </summary>
    private bool IsContextSynonym(string word1, string word2)
    {
        var contextMappings = new Dictionary<string, string[]>
        {
            { "user", new[] { "user", "account", "aduser" } },
            { "group", new[] { "group", "adgroup", "groupmember", "adgroupmember" } },
            { "computer", new[] { "computer", "adcomputer", "machine" } },
            { "password", new[] { "password", "accountpassword", "adaccountpassword" } },
            { "account", new[] { "account", "user", "adaccount", "aduser" } },
            { "permission", new[] { "permission", "access", "authorization", "policy" } }
        };

        // Check if both words belong to the same context group
        foreach (var mapping in contextMappings.Values)
        {
            if (mapping.Contains(word1.ToLowerInvariant()) && mapping.Contains(word2.ToLowerInvariant()))
            {
                return true;
            }
        }

        return false;
    }



    /// <summary>
    /// NEW FLOW: Get full command details from the comprehensive dataset
    /// </summary>
    private async Task<string?> GetCommandDetailsFromDatasetAsync(string commandName)
    {
        try
        {
            var datasetPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, AimxConstants.FilePaths.ComprehensiveDatasetPath);

            if (!File.Exists(datasetPath))
            {
                _logger.LogError("Dataset file not found: {DatasetPath}", datasetPath);
                return null;
            }

            var jsonContent = await File.ReadAllTextAsync(datasetPath);
            var commands = System.Text.Json.JsonSerializer.Deserialize<List<ComprehensiveDatasetCommand>>(jsonContent, new JsonSerializerOptions { PropertyNameCaseInsensitive = true });

            var command = commands?.FirstOrDefault(c =>
                string.Equals(c.CommandName, commandName, StringComparison.OrdinalIgnoreCase));

            if (command != null)
            {
                _logger.LogInformation("Found command details for: {CommandName}", commandName);

                // Convert the comprehensive command data to JSON string for the LLM
                var commandJson = System.Text.Json.JsonSerializer.Serialize(command, new JsonSerializerOptions
                {
                    WriteIndented = true,
                    PropertyNameCaseInsensitive = true
                });

                return commandJson;
            }

            _logger.LogWarning("Command not found in dataset: {CommandName}", commandName);
            return null;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error reading command details from dataset for: {CommandName}", commandName);
            return null;
        }
    }

    /// <summary>
    /// ENHANCED: Generate final PowerShell command with parameters using LLM + Rich Command Context
    /// </summary>
    private async Task<string?> GenerateFinalCommandAsync(string stepDescription, string commandDetails)
    {
        try
        {
            // EXTRACT COMMAND NAME from commandDetails to get rich context
            string? commandName = null;
            try
            {
                var commandDoc = JsonSerializer.Deserialize<ComprehensiveDatasetCommand>(commandDetails, new JsonSerializerOptions { PropertyNameCaseInsensitive = true });
                commandName = commandDoc?.CommandName;
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Could not extract command name from command details");
            }

            // GET RICH COMMAND CONTEXT from cache
            var richContext = GetCommandContext(commandName);
            if (richContext == null)
            {
                _logger.LogWarning("No rich context found for command: {CommandName}", commandName);
                return null;
            }

            _logger.LogInformation("Enhanced prompt with rich context for command: {CommandName}", commandName);

            // Create the structured JSON format for LLM prompt
            var formatDescription = @"The following JSON object describes the full usage information of a PowerShell cmdlet. Each field gives structured information about how to correctly use the command.

JSON Format Explanation:
- ""CommandName"": The name of the PowerShell cmdlet (e.g., ""Get-ADUser"").
- ""Syntax"": A list of valid syntax patterns the cmdlet can follow. Each string represents one possible usage pattern.
- ""Parameters"": A list of parameter definitions. Each item contains:
    - ""Name"": The name of the parameter (e.g., ""Filter"", ""Identity"").
    - ""Description"": A plain-text explanation of what the parameter does.
    - ""Details"": Metadata about the parameter, including:
        - ""Required"": Whether this parameter is mandatory.
        - ""Type"": The data type of the parameter (e.g., ""String"", ""PSCredential"").
        - ""AcceptPipelineInput"": Whether the cmdlet can accept this parameter from pipeline input.
        - ""Default"": The default value if not specified.
        - ""Position"": Whether this is a positional or named parameter.
- ""Examples"": A list of example usages of the cmdlet. Each example includes:
    - ""Code"": A PowerShell command line using the cmdlet.
    - ""Description"": A short explanation of what the command does.

Use this JSON to:
- Match user requests to the correct parameters and syntax.
- Validate required parameters.
- Choose optimal usage patterns.
- Learn correct examples of how the command is used in practice.";

            var commandInfoJson = JsonSerializer.Serialize(richContext, new JsonSerializerOptions
            {
                WriteIndented = true,
                PropertyNameCaseInsensitive = true
            });

            var structuredPrompt = JsonSerializer.Serialize(new
            {
                system = "You are a PowerShell command assistant. Use the structured command information to answer queries about how to construct commands.",
                context = "The following explains the JSON format that will be provided:",
                formatDescription = formatDescription,
                commandInfo = richContext,
                user = stepDescription
            }, new JsonSerializerOptions
            {
                WriteIndented = true,
                PropertyNameCaseInsensitive = true
            });

            var enhancedPrompt = $@"You are a PowerShell command assistant. Use the structured command information to construct the correct PowerShell command.

{structuredPrompt}

CRITICAL RULES:
1. **Extract the EXACT username/identifier from the user request** - do not use example names
2. **Space Validation**: If the identifier contains spaces and does NOT start with ""CN="", use -Filter instead of -Identity
3. **Parameter Minimalism**: Only add parameters the user explicitly requested

Generate ONLY the PowerShell command - no explanations or additional text.

PowerShell Command:";

            var llmResponse = await CallLlmAsync(enhancedPrompt);

            if (string.IsNullOrWhiteSpace(llmResponse))
            {
                _logger.LogWarning("LLM returned empty response for command generation");
                return null;
            }

            // Clean up the response to extract just the PowerShell command
            var cleanCommand = llmResponse.Trim()
                .Replace("```powershell", "")
                .Replace("```", "")
                .Replace("PowerShell", "")
                .Trim();

            _logger.LogInformation("Generated PowerShell command: {Command}", cleanCommand);
            return cleanCommand;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error generating final command for step: {StepDescription}", stepDescription);
            return null;
        }
    }

    /// <summary>
    /// Create an error workflow when command generation fails
    /// </summary>
    private ExecutableWorkflow CreateErrorWorkflow(UserGoal userGoal, string errorMessage)
    {
        return new ExecutableWorkflow
        {
            WorkflowName = "Error Workflow",
            Description = $"Error generating workflow: {errorMessage}",
            Steps = new List<WorkflowStep>(),
            CreatedBy = userGoal.Context.GetValueOrDefault("userId", "system"),
            PlanningConfidence = 0.0,
            PlanningMethod = "error",
            RiskLevel = "low"
        };
    }

    /// <summary>
    /// Call LLM for complex reasoning and analysis
    /// </summary>
    private async Task<string> CallLlmAsync(string prompt)
    {
        try
        {
            using var httpClient = _httpClientFactory.CreateClient();
            httpClient.Timeout = TimeSpan.FromSeconds(AimxConstants.Timeouts.ShortHttpTimeoutSeconds);

            var requestBody = new
            {
                model = AimxConstants.ModelConfig.CurrentLlmModel,
                messages = new[]
                {
                    new { role = "system", content = "You are an IT expert. Answer concisely." },
                    new { role = "user", content = prompt }
                },
                max_tokens = AimxConstants.ModelConfig.LargeMaxTokens,
                temperature = AimxConstants.ModelConfig.HighTemperature,
                top_k = AimxConstants.ModelConfig.TopK,
                top_p = AimxConstants.ModelConfig.TopP,
                stream = false
            };

            var json = JsonSerializer.Serialize(requestBody);
            var content = new StringContent(json, System.Text.Encoding.UTF8, "application/json");

            // COMPREHENSIVE LLM PAYLOAD LOGGING
            var timestamp = DateTime.UtcNow.ToString("yyyy-MM-dd_HH-mm-ss-fff");
            var logFileName = $"llm_request_{timestamp}_{Guid.NewGuid().ToString("N")[..8]}.json";
            var logFilePath = Path.Combine("logs", "llm_requests", logFileName);

            try
            {
                Directory.CreateDirectory(Path.GetDirectoryName(logFilePath)!);
                await File.WriteAllTextAsync(logFilePath, json);
                _logger.LogInformation("LLM REQUEST LOGGED: {LogFile} | Prompt Length: {PromptLength} chars",
                    logFileName, prompt.Length);
            }
            catch (Exception logEx)
            {
                _logger.LogWarning(logEx, "Failed to log LLM request to file");
            }

            // Also log to console for immediate visibility
            _logger.LogInformation("LLM REQUEST PAYLOAD:\n{Payload}", json);

            var response = await httpClient.PostAsync($"{AimxConstants.ServiceEndpoints.LlmServiceBaseUrl}{AimxConstants.ApiEndpoints.ChatCompletions}", content);

            if (!response.IsSuccessStatusCode)
            {
                _logger.LogError("LLM service returned {StatusCode}: {ReasonPhrase}",
                    response.StatusCode, response.ReasonPhrase);
                throw new HttpRequestException($"LLM service error: {response.StatusCode}");
            }

            var responseContent = await response.Content.ReadAsStringAsync();

            // LOG LLM RESPONSE
            _logger.LogInformation("LLM RESPONSE PAYLOAD:\n{Response}", responseContent);

            var options = new JsonSerializerOptions { PropertyNameCaseInsensitive = true };
            var llmResponse = JsonSerializer.Deserialize<LlmResponse>(responseContent, options);

            return llmResponse?.Choices?.FirstOrDefault()?.Message?.Content ?? "";
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error calling LLM service");
            return "fallback_operation";
        }
    }

    /// <summary>
    /// Check if operations match contextually (e.g., user/group operations)
    /// </summary>
    private bool IsContextualMatch(string requiredOperation, string toolOperation)
    {
        // Context mappings for related operations
        var contextMappings = new Dictionary<string, string[]>
        {
            { "user", new[] { "user", "account", "aduser" } },
            { "group", new[] { "group", "adgroup", "groupmember", "adgroupmember" } },
            { "computer", new[] { "computer", "adcomputer", "machine" } },
            { "password", new[] { "password", "accountpassword", "adaccountpassword" } },
            { "account", new[] { "account", "user", "adaccount", "aduser" } },
            { "permission", new[] { "permission", "access", "authorization", "policy" } }
        };

        // Extract context words from both operations
        var requiredWords = requiredOperation.Split('_');
        var toolWords = toolOperation.Split('_');

        // Check if any context words match
        foreach (var requiredWord in requiredWords)
        {
            if (contextMappings.TryGetValue(requiredWord, out var contextWords))
            {
                if (toolWords.Any(toolWord => contextWords.Any(context => toolWord.Contains(context))))
                {
                    return true;
                }
            }
        }

        return false;
    }

    /// <summary>
    /// Generate a fallback step when LLM service is unavailable
    /// </summary>
    private string GenerateFallbackStep(string userGoal)
    {
        // Simple rule-based step generation based on common patterns
        var goal = userGoal.ToLowerInvariant();

        if (goal.Contains("create") && goal.Contains("user"))
            return "Create new Active Directory user account";
        else if (goal.Contains("delete") && goal.Contains("user"))
            return "Delete Active Directory user account";
        else if (goal.Contains("disable") && goal.Contains("user"))
            return "Disable Active Directory user account";
        else if (goal.Contains("enable") && goal.Contains("user"))
            return "Enable Active Directory user account";
        else if (goal.Contains("reset") && goal.Contains("password"))
            return "Reset Active Directory user password";
        else if (goal.Contains("unlock") && goal.Contains("account"))
            return "Unlock Active Directory user account";
        else if (goal.Contains("add") && goal.Contains("group"))
            return "Add user to Active Directory group";
        else if (goal.Contains("remove") && goal.Contains("group"))
            return "Remove user from Active Directory group";
        else if (goal.Contains("create") && goal.Contains("group"))
            return "Create new Active Directory group";
        else if (goal.Contains("delete") && goal.Contains("group"))
            return "Delete Active Directory group";
        else
            return $"Perform IT operation: {userGoal}";
    }

    /// <summary>
    /// Determine if a command requires approval based on its risk level
    /// </summary>
    private bool DetermineIfApprovalRequired(string command)
    {
        if (string.IsNullOrEmpty(command))
            return true;

        var lowerCommand = command.ToLowerInvariant();

        // High-risk operations that require approval
        var highRiskKeywords = new[] { "remove", "delete", "disable", "reset", "clear", "move" };

        return highRiskKeywords.Any(keyword => lowerCommand.Contains(keyword));
    }

    /// <summary>
    /// Create the final executable workflow
    /// </summary>
    private ExecutableWorkflow CreateExecutableWorkflow(UserGoal userGoal, List<WorkflowStep> steps)
    {
        var workflow = new ExecutableWorkflow
        {
            WorkflowName = $"Workflow for: {userGoal.PrimaryObjective}",
            Description = $"Automated workflow to achieve: {userGoal.PrimaryObjective}",
            Steps = steps,
            CreatedBy = userGoal.Context.GetValueOrDefault("userId", "system"),
            PlanningMethod = "llm_driven"
        };

        // Set required permissions from all steps
        workflow.RequiredPermissions = steps
            .SelectMany(step => step.Parameters.GetValueOrDefault("requiredPermissions") as List<string> ?? new List<string>())
            .Distinct()
            .ToList();

        // Set error handling strategies
        workflow.ErrorHandling["default"] = "stop_and_rollback";
        workflow.ErrorHandling["timeout"] = "retry_with_backoff";
        workflow.ErrorHandling["permission_denied"] = "escalate_for_approval";

        return workflow;
    }

    #endregion
}

/// <summary>
/// Represents a required operation identified from user goals
/// </summary>
public class RequiredOperation
{
    public string OperationType { get; set; } = string.Empty;
    public string Category { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public int Priority { get; set; } = 1;
}

/// <summary>
/// Represents the result of workflow validation
/// </summary>
public class WorkflowValidationResult
{
    public bool IsValid { get; set; } = true;
    public List<string> Errors { get; set; } = new();
    public List<string> Warnings { get; set; } = new();
}
