/*++

Copyright (c) Microsoft Corporation. All rights reserved.

Module Name:

    ToolDiscoveryService.cs

Abstract:

    Service for discovering and managing available PowerShell tools and commands.
    Interfaces with NetRag service for command search and retrieval.

Author:

    <PERSON><PERSON><PERSON> (rizhang) 07/29/2025

Environment:

    User mode only.

--*/

using IntentPlanningService.Configuration;
using IntentPlanningService.Models;
using System.Text.Json;
using AimxShared.Constants;

namespace IntentPlanningService.Services;

/// <summary>
/// PowerShell command result from NetRag service
/// </summary>
public class PowerShellCommandResult
{
    public string CommandName { get; set; } = string.Empty;
    public float Score { get; set; }
    public string FullText { get; set; } = string.Empty;
    public int ParameterCount { get; set; }
    public int ExampleCount { get; set; }
    public string ParameterNames { get; set; } = string.Empty;
    public string Id { get; set; } = string.Empty;
    public Dictionary<string, object> Metadata { get; set; } = new();
}

/// <summary>
/// Service for discovering and managing available IT operation tools
/// </summary>
public class ToolDiscoveryService
{
    private readonly ILogger<ToolDiscoveryService> _logger;
    private readonly IntentPlanningConfiguration _config;
    private readonly HttpClient _httpClient;
    private readonly Dictionary<string, AvailableTool> _toolCache;
    private readonly SemaphoreSlim _semaphore;
    private DateTime _lastCacheUpdate = DateTime.MinValue;

    public ToolDiscoveryService(
        ILogger<ToolDiscoveryService> logger,
        IntentPlanningConfiguration config,
        HttpClient httpClient)
    {
        _logger = logger;
        _config = config;
        _httpClient = httpClient;
        _toolCache = new Dictionary<string, AvailableTool>();
        _semaphore = new SemaphoreSlim(1, 1);

        // Configure HTTP client for NetRag service (PowerShell command discovery)
        _httpClient.BaseAddress = new Uri("http://localhost:5000");
        _httpClient.Timeout = TimeSpan.FromSeconds(30);
    }

    /// <summary>
    /// Discover available tools from the Universal Tool Manager
    /// </summary>
    public async Task<List<AvailableTool>> DiscoverAvailableToolsAsync(ToolDiscoveryRequest? request = null)
    {
        await _semaphore.WaitAsync();
        try
        {
            _logger.LogInformation("Discovering available tools from Tool Manager");

            // Check cache first
            if (IsCacheValid() && _toolCache.Any())
            {
                _logger.LogDebug("Returning {Count} tools from cache", _toolCache.Count);
                return FilterTools(_toolCache.Values.ToList(), request);
            }

            // Fetch tools from Universal Tool Manager service
            var tools = await FetchToolsFromServiceAsync(request);

            // Update cache
            await UpdateToolCacheAsync(tools);

            _logger.LogInformation("Discovered {Count} available tools", tools.Count);
            return tools;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error discovering available tools");
            
            // Return cached tools if available, even if stale
            if (_toolCache.Any())
            {
                _logger.LogWarning("Returning stale cached tools due to discovery error");
                return FilterTools(_toolCache.Values.ToList(), request);
            }

            // Return empty list if no cache available
            return new List<AvailableTool>();
        }
        finally
        {
            _semaphore.Release();
        }
    }

    /// <summary>
    /// Get a specific tool by ID
    /// </summary>
    public async Task<AvailableTool?> GetToolAsync(string toolId)
    {
        try
        {
            var tools = await DiscoverAvailableToolsAsync();
            return tools.FirstOrDefault(t => t.ToolId == toolId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting tool {ToolId}", toolId);
            return null;
        }
    }

    /// <summary>
    /// Get tools by category
    /// </summary>
    public async Task<List<AvailableTool>> GetToolsByCategoryAsync(string category)
    {
        try
        {
            var request = new ToolDiscoveryRequest { Category = category };
            return await DiscoverAvailableToolsAsync(request);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting tools for category {Category}", category);
            return new List<AvailableTool>();
        }
    }

    /// <summary>
    /// Get tools with specific capabilities
    /// </summary>
    public async Task<List<AvailableTool>> GetToolsWithCapabilitiesAsync(List<string> requiredCapabilities)
    {
        try
        {
            var request = new ToolDiscoveryRequest { RequiredCapabilities = requiredCapabilities };
            return await DiscoverAvailableToolsAsync(request);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting tools with capabilities {Capabilities}", 
                string.Join(", ", requiredCapabilities));
            return new List<AvailableTool>();
        }
    }

    /// <summary>
    /// Check health status of all tools
    /// </summary>
    public async Task<Dictionary<string, string>> CheckToolHealthAsync()
    {
        try
        {
            _logger.LogInformation("Checking health status of all tools");

            var healthStatus = new Dictionary<string, string>();
            var tools = await DiscoverAvailableToolsAsync(new ToolDiscoveryRequest { IncludeHealthStatus = true });

            foreach (var tool in tools)
            {
                healthStatus[tool.ToolId] = tool.HealthStatus;
            }

            _logger.LogInformation("Health check completed for {Count} tools", tools.Count);
            return healthStatus;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking tool health");
            return new Dictionary<string, string>();
        }
    }

    /// <summary>
    /// Refresh the tool cache
    /// </summary>
    public async Task RefreshToolCacheAsync()
    {
        await _semaphore.WaitAsync();
        try
        {
            _logger.LogInformation("Refreshing tool cache");
            
            _toolCache.Clear();
            _lastCacheUpdate = DateTime.MinValue;
            
            await DiscoverAvailableToolsAsync();
            
            _logger.LogInformation("Tool cache refreshed with {Count} tools", _toolCache.Count);
        }
        finally
        {
            _semaphore.Release();
        }
    }

    #region Private Methods

    /// <summary>
    /// Fetch PowerShell tools from NetRag service
    /// </summary>
    private async Task<List<AvailableTool>> FetchToolsFromServiceAsync(ToolDiscoveryRequest? request)
    {
        try
        {
            _logger.LogDebug("Fetching PowerShell commands from NetRag service");

            // Get PowerShell commands using search endpoint with a broad query
            var searchResponse = await _httpClient.GetAsync($"{AimxConstants.ApiEndpoints.PowerShellCommandSearch}?{AimxConstants.QueryParameters.Query}={AimxConstants.DefaultQueries.PowerShellActiveDirectoryCommand}&{AimxConstants.QueryParameters.Limit}=3");

            if (!searchResponse.IsSuccessStatusCode)
            {
                _logger.LogWarning("NetRag service returned {StatusCode}: {ReasonPhrase}",
                    searchResponse.StatusCode, searchResponse.ReasonPhrase);
                return new List<AvailableTool>();
            }

            var searchContent = await searchResponse.Content.ReadAsStringAsync();
            var options = new JsonSerializerOptions
            {
                PropertyNameCaseInsensitive = true
            };
            var commandResults = JsonSerializer.Deserialize<List<PowerShellCommandResult>>(searchContent, options) ?? new List<PowerShellCommandResult>();

            _logger.LogInformation("Retrieved {Count} PowerShell commands from NetRag", commandResults.Count);

            // Debug: Log the actual command data
            foreach (var cmd in commandResults)
            {
                _logger.LogDebug("Command: {CommandName}, Score: {Score}, ParameterCount: {ParameterCount}, FullText length: {FullTextLength}",
                    cmd.CommandName, cmd.Score, cmd.ParameterCount, cmd.FullText?.Length ?? 0);
            }

            // Convert PowerShell commands to AvailableTool format
            var tools = new List<AvailableTool>();

            foreach (var commandResult in commandResults)
            {
                var tool = await ConvertPowerShellCommandResultToToolAsync(commandResult);
                if (tool != null)
                {
                    tools.Add(tool);
                    _logger.LogDebug("Successfully converted command {CommandName} to tool", commandResult.CommandName);
                }
                else
                {
                    _logger.LogWarning("Failed to convert command {CommandName} to tool", commandResult.CommandName ?? "null");
                }
            }

            // Apply filters if provided
            if (request != null)
            {
                tools = FilterTools(tools, request);
            }

            _logger.LogInformation("Converted {Count} PowerShell commands to tools", tools.Count);
            return tools;
        }
        catch (HttpRequestException ex)
        {
            _logger.LogError(ex, "HTTP error contacting NetRag service");
            return new List<AvailableTool>();
        }
        catch (TaskCanceledException ex)
        {
            _logger.LogError(ex, "Timeout contacting NetRag service");
            return new List<AvailableTool>();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Unexpected error fetching tools from NetRag service");
            return new List<AvailableTool>();
        }
    }

    /// <summary>
    /// Convert PowerShell command result to AvailableTool format
    /// </summary>
    private async Task<AvailableTool?> ConvertPowerShellCommandResultToToolAsync(PowerShellCommandResult commandResult)
    {
        try
        {
            if (commandResult == null || string.IsNullOrEmpty(commandResult.CommandName))
            {
                return null;
            }

            // Extract verb and noun from command name
            var parts = commandResult.CommandName.Split('-', 2);
            var verb = parts.Length > 0 ? parts[0] : "";
            var noun = parts.Length > 1 ? parts[1] : "";

            // Determine category based on noun
            var category = DetermineCategory(noun);

            // Create tool operation
            var operation = new ToolOperation
            {
                OperationId = commandResult.CommandName.ToLowerInvariant().Replace("-", "_"),
                OperationName = commandResult.CommandName,
                Description = ExtractDescription(commandResult.FullText),
                EstimatedDurationSeconds = EstimateDuration(verb),
                RiskLevel = DetermineRiskLevel(verb, noun),
                RequiredPermissions = new List<string> { "AD_POWERSHELL_EXECUTION" },
                SupportsRollback = SupportsRollback(verb),
                Parameters = ExtractParameters(commandResult.ParameterNames)
            };

            // Create available tool
            var tool = new AvailableTool
            {
                ToolId = $"ps_{commandResult.CommandName.ToLowerInvariant().Replace("-", "_")}",
                ToolName = $"PowerShell {commandResult.CommandName}",
                Description = $"Execute PowerShell command: {commandResult.CommandName}",
                Category = category,
                IsAvailable = true,
                HealthStatus = "healthy",
                Operations = new List<ToolOperation> { operation },
                Capabilities = new ToolCapabilities
                {
                    SupportsParallelExecution = false,
                    SupportsBatching = false,
                    SupportsRollback = SupportsRollback(verb),
                    MaxConcurrentOperations = 1,
                    MaxBatchSize = 1,
                    SupportedEnvironments = new List<string> { "production", "staging", "test", "development" }
                }
            };

            return tool;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error converting PowerShell command {CommandName} to tool", commandResult?.CommandName ?? "Unknown");
            return null;
        }
    }

    /// <summary>
    /// Determine category based on PowerShell command noun
    /// </summary>
    private string DetermineCategory(string noun)
    {
        return noun.ToLowerInvariant() switch
        {
            var n when n.Contains("user") => "user_management",
            var n when n.Contains("group") => "group_management",
            var n when n.Contains("computer") => "computer_management",
            var n when n.Contains("ou") || n.Contains("organizationalunit") => "organizational_unit_management",
            var n when n.Contains("domain") => "domain_management",
            var n when n.Contains("forest") => "forest_management",
            var n when n.Contains("gpo") || n.Contains("grouppolicy") => "group_policy_management",
            var n when n.Contains("replication") => "replication_management",
            var n when n.Contains("trust") => "trust_management",
            var n when n.Contains("service") => "service_management",
            _ => "active_directory"
        };
    }

    /// <summary>
    /// Estimate duration based on PowerShell command verb
    /// </summary>
    private int EstimateDuration(string verb)
    {
        return verb.ToLowerInvariant() switch
        {
            "get" => 10,
            "find" => 15,
            "search" => 20,
            "test" => 15,
            "new" => 30,
            "set" => 25,
            "add" => 20,
            "remove" => 25,
            "disable" => 20,
            "enable" => 20,
            "move" => 30,
            "rename" => 25,
            "reset" => 30,
            "unlock" => 15,
            "clear" => 20,
            _ => 30
        };
    }

    /// <summary>
    /// Determine risk level based on PowerShell command verb and noun
    /// </summary>
    private string DetermineRiskLevel(string verb, string noun)
    {
        var verbLower = verb.ToLowerInvariant();
        var nounLower = noun.ToLowerInvariant();

        // High risk operations
        if (verbLower is "remove" or "delete" or "clear" or "reset")
            return "high";

        // Critical risk for domain/forest operations
        if (nounLower.Contains("domain") || nounLower.Contains("forest"))
            return "critical";

        // Medium risk for create/modify operations
        if (verbLower is "new" or "set" or "add" or "move" or "rename" or "disable")
            return "medium";

        // Low risk for read operations
        return "low";
    }

    /// <summary>
    /// Check if command supports rollback based on verb
    /// </summary>
    private bool SupportsRollback(string verb)
    {
        return verb.ToLowerInvariant() switch
        {
            "new" => true,      // Can be rolled back with Remove
            "add" => true,      // Can be rolled back with Remove
            "set" => false,     // Difficult to rollback property changes
            "enable" => true,   // Can be rolled back with Disable
            "disable" => true,  // Can be rolled back with Enable
            "move" => true,     // Can be moved back
            "rename" => false,  // Difficult to rollback renames
            _ => false
        };
    }
    /// <summary>
    /// Extract description from PowerShell command full text
    /// </summary>
    private string ExtractDescription(string fullText)
    {
        if (string.IsNullOrEmpty(fullText))
            return "PowerShell command";

        // Try to extract synopsis or first meaningful line
        var lines = fullText.Split('\n', StringSplitOptions.RemoveEmptyEntries);
        foreach (var line in lines)
        {
            var trimmed = line.Trim();
            if (trimmed.Length > 10 && !trimmed.StartsWith("NAME") && !trimmed.StartsWith("SYNTAX"))
            {
                return trimmed.Length > 100 ? trimmed.Substring(0, 97) + "..." : trimmed;
            }
        }

        return "PowerShell command";
    }

    /// <summary>
    /// Extract parameters from parameter names string (comma-separated)
    /// </summary>
    private List<OperationParameter> ExtractParameters(string parameterNames)
    {
        var parameters = new List<OperationParameter>();

        if (string.IsNullOrWhiteSpace(parameterNames))
        {
            return parameters;
        }

        var paramNames = parameterNames.Split(',', StringSplitOptions.RemoveEmptyEntries)
                                      .Select(p => p.Trim())
                                      .Where(p => !string.IsNullOrEmpty(p));

        foreach (var paramName in paramNames)
        {
            parameters.Add(new OperationParameter
            {
                ParameterName = paramName,
                ParameterType = "string", // Default type, could be enhanced with more detailed parsing
                IsRequired = false, // Would need more detailed parsing to determine
                Description = $"Parameter: {paramName}"
            });
        }

        return parameters;
    }

    /// <summary>
    /// Update the tool cache with new tools
    /// </summary>
    private Task UpdateToolCacheAsync(List<AvailableTool> tools)
    {
        _toolCache.Clear();

        foreach (var tool in tools)
        {
            _toolCache[tool.ToolId] = tool;
        }

        _lastCacheUpdate = DateTime.UtcNow;

        _logger.LogDebug("Updated tool cache with {Count} tools", tools.Count);

        return Task.CompletedTask;
    }

    /// <summary>
    /// Check if the tool cache is still valid
    /// </summary>
    private bool IsCacheValid()
    {
        var cacheAge = DateTime.UtcNow - _lastCacheUpdate;
        var maxCacheAge = TimeSpan.FromMinutes(5); // Cache for 5 minutes

        return cacheAge < maxCacheAge;
    }

    /// <summary>
    /// Filter tools based on discovery request criteria
    /// </summary>
    private List<AvailableTool> FilterTools(List<AvailableTool> tools, ToolDiscoveryRequest? request)
    {
        if (request == null)
            return tools;

        var filteredTools = tools.AsEnumerable();

        // Filter by category
        if (!string.IsNullOrEmpty(request.Category))
        {
            filteredTools = filteredTools.Where(t => t.Category.Equals(request.Category, StringComparison.OrdinalIgnoreCase));
        }

        // Filter by required capabilities
        if (request.RequiredCapabilities.Any())
        {
            filteredTools = filteredTools.Where(tool =>
                request.RequiredCapabilities.All(capability =>
                    HasCapability(tool, capability)));
        }

        // Filter by environment support
        if (!string.IsNullOrEmpty(request.Environment))
        {
            filteredTools = filteredTools.Where(tool =>
                tool.Capabilities.SupportedEnvironments.Contains(request.Environment));
        }

        return filteredTools.ToList();
    }

    /// <summary>
    /// Check if a tool has a specific capability
    /// </summary>
    private bool HasCapability(AvailableTool tool, string capability)
    {
        return capability.ToLowerInvariant() switch
        {
            "parallel_execution" => tool.Capabilities.SupportsParallelExecution,
            "batching" => tool.Capabilities.SupportsBatching,
            "streaming" => tool.Capabilities.SupportsStreaming,
            "rollback" => tool.Capabilities.SupportsRollback,
            _ => false
        };
    }

    #endregion
}
