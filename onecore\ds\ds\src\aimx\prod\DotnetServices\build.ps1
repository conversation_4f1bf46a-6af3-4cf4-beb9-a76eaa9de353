#
# AIMX DotnetServices Build Script
# Author: <PERSON><PERSON><PERSON> (rizhang)
# Date: 07/29/2025
#
# This script provides comprehensive build automation for all AIMX .NET services
#

param(
    [Parameter(HelpMessage="Build configuration (Debug/Release)")]
    [ValidateSet("Debug", "Release")]
    [string]$Configuration = "Release",
    
    [Parameter(HelpMessage="Target runtime identifier")]
    [string]$Runtime = "win-x64",
    
    [Parameter(HelpMessage="Skip tests during build")]
    [switch]$SkipTests,
    
    [Parameter(HelpMessage="Clean before build")]
    [switch]$Clean,
    
    [Parameter(HelpMessage="Create deployment packages")]
    [switch]$Package,
    
    [Parameter(HelpMessage="Verbose output")]
    [switch]$Verbose,
    
    [Parameter(HelpMessage="Build specific project only")]
    [ValidateSet("All", "IntentPlanningService", "NetRagService", "DatabaseConverter", "Shared")]
    [string]$Project = "All"
)

# Script configuration
$ErrorActionPreference = "Stop"
$ProgressPreference = "SilentlyContinue"

# Build paths
$SolutionFile = "DotnetServices.sln"
$ArtifactsDir = "artifacts"
$PublishDir = "$ArtifactsDir\publish"
$PackagesDir = "$ArtifactsDir\packages"

# Colors for output
$ColorSuccess = "Green"
$ColorWarning = "Yellow"
$ColorError = "Red"
$ColorInfo = "Cyan"

function Write-BuildMessage {
    param(
        [string]$Message,
        [string]$Color = "White",
        [string]$Prefix = "INFO"
    )
    
    $timestamp = Get-Date -Format "HH:mm:ss"
    Write-Host "[$timestamp] [$Prefix] $Message" -ForegroundColor $Color
}

function Write-BuildSuccess {
    param([string]$Message)
    Write-BuildMessage -Message $Message -Color $ColorSuccess -Prefix "SUCCESS"
}

function Write-BuildWarning {
    param([string]$Message)
    Write-BuildMessage -Message $Message -Color $ColorWarning -Prefix "WARNING"
}

function Write-BuildError {
    param([string]$Message)
    Write-BuildMessage -Message $Message -Color $ColorError -Prefix "ERROR"
}

function Write-BuildInfo {
    param([string]$Message)
    Write-BuildMessage -Message $Message -Color $ColorInfo -Prefix "INFO"
}

function Test-Prerequisites {
    Write-BuildInfo "Checking prerequisites..."
    
    # Check .NET SDK
    try {
        $dotnetVersion = dotnet --version
        Write-BuildInfo ".NET SDK version: $dotnetVersion"
    }
    catch {
        Write-BuildError ".NET SDK not found. Please install .NET 8.0 SDK."
        exit 1
    }
    
    # Check solution file
    if (-not (Test-Path $SolutionFile)) {
        Write-BuildError "Solution file '$SolutionFile' not found."
        exit 1
    }
    
    Write-BuildSuccess "Prerequisites check completed."
}

function Initialize-BuildEnvironment {
    Write-BuildInfo "Initializing build environment..."
    
    # Create artifacts directories
    @($ArtifactsDir, $PublishDir, $PackagesDir) | ForEach-Object {
        if (-not (Test-Path $_)) {
            New-Item -ItemType Directory -Path $_ -Force | Out-Null
            Write-BuildInfo "Created directory: $_"
        }
    }
    
    # Set environment variables
    $env:DOTNET_CLI_TELEMETRY_OPTOUT = "1"
    $env:DOTNET_SKIP_FIRST_TIME_EXPERIENCE = "1"
    
    Write-BuildSuccess "Build environment initialized."
}

function Invoke-Clean {
    if ($Clean) {
        Write-BuildInfo "Cleaning solution..."
        
        # Clean solution
        dotnet clean $SolutionFile --configuration $Configuration --verbosity minimal
        
        # Remove artifacts
        if (Test-Path $ArtifactsDir) {
            Remove-Item -Path $ArtifactsDir -Recurse -Force
            Write-BuildInfo "Removed artifacts directory."
        }
        
        # Remove bin/obj directories
        Get-ChildItem -Path . -Recurse -Directory -Name "bin", "obj" | ForEach-Object {
            $fullPath = $_.FullName
            if (Test-Path $fullPath) {
                Remove-Item -Path $fullPath -Recurse -Force
                Write-BuildInfo "Removed: $fullPath"
            }
        }
        
        Write-BuildSuccess "Clean completed."
    }
}

function Invoke-Restore {
    Write-BuildInfo "Restoring NuGet packages..."
    
    $restoreArgs = @(
        "restore"
        $SolutionFile
        "--verbosity", "minimal"
    )
    
    if ($Verbose) {
        $restoreArgs[2] = "normal"
    }
    
    & dotnet @restoreArgs
    
    if ($LASTEXITCODE -ne 0) {
        Write-BuildError "Package restore failed."
        exit $LASTEXITCODE
    }
    
    Write-BuildSuccess "Package restore completed."
}

function Invoke-Build {
    Write-BuildInfo "Building solution..."
    
    $buildArgs = @(
        "build"
        $SolutionFile
        "--configuration", $Configuration
        "--no-restore"
        "--verbosity", "minimal"
    )
    
    if ($Verbose) {
        $buildArgs[6] = "normal"
    }
    
    if ($Project -ne "All") {
        # Build specific project
        $projectPath = switch ($Project) {
            "IntentPlanningService" { "IntentPlanningService\IntentPlanningService.csproj" }
            "NetRagService" { "NetRagService\NetRagService.csproj" }
            "DatabaseConverter" { "DatabaseConverter\DatabaseConverter.csproj" }
            "Shared" { "Shared\AimxShared.csproj" }
        }
        $buildArgs[1] = $projectPath
        Write-BuildInfo "Building project: $Project"
    }
    
    & dotnet @buildArgs
    
    if ($LASTEXITCODE -ne 0) {
        Write-BuildError "Build failed."
        exit $LASTEXITCODE
    }
    
    Write-BuildSuccess "Build completed."
}

function Invoke-Tests {
    if (-not $SkipTests) {
        Write-BuildInfo "Running tests..."
        
        # Check if test projects exist
        $testProjects = Get-ChildItem -Path . -Recurse -Filter "*.Tests.csproj"
        
        if ($testProjects.Count -eq 0) {
            Write-BuildWarning "No test projects found. Skipping tests."
            return
        }
        
        $testArgs = @(
            "test"
            $SolutionFile
            "--configuration", $Configuration
            "--no-build"
            "--verbosity", "minimal"
            "--logger", "console;verbosity=normal"
        )
        
        if ($Verbose) {
            $testArgs[6] = "normal"
        }
        
        & dotnet @testArgs
        
        if ($LASTEXITCODE -ne 0) {
            Write-BuildError "Tests failed."
            exit $LASTEXITCODE
        }
        
        Write-BuildSuccess "Tests completed."
    }
    else {
        Write-BuildWarning "Tests skipped."
    }
}

function Invoke-Publish {
    if ($Package) {
        Write-BuildInfo "Publishing applications..."
        
        $services = @(
            @{ Name = "IntentPlanningService"; Path = "IntentPlanningService\IntentPlanningService.csproj" },
            @{ Name = "NetRagService"; Path = "NetRagService\NetRagService.csproj" },
            @{ Name = "DatabaseConverter"; Path = "DatabaseConverter\DatabaseConverter.csproj" }
        )
        
        foreach ($service in $services) {
            if ($Project -eq "All" -or $Project -eq $service.Name) {
                Write-BuildInfo "Publishing $($service.Name)..."
                
                $publishPath = "$PublishDir\$($service.Name)"
                
                $publishArgs = @(
                    "publish"
                    $service.Path
                    "--configuration", $Configuration
                    "--runtime", $Runtime
                    "--self-contained", "true"
                    "--output", $publishPath
                    "--verbosity", "minimal"
                )
                
                if ($Verbose) {
                    $publishArgs[10] = "normal"
                }
                
                & dotnet @publishArgs
                
                if ($LASTEXITCODE -ne 0) {
                    Write-BuildError "Publish failed for $($service.Name)."
                    exit $LASTEXITCODE
                }
                
                Write-BuildSuccess "$($service.Name) published to: $publishPath"
            }
        }
        
        Write-BuildSuccess "Publishing completed."
    }
}

function Show-BuildSummary {
    Write-BuildInfo "Build Summary:"
    Write-Host "  Configuration: $Configuration" -ForegroundColor White
    Write-Host "  Runtime: $Runtime" -ForegroundColor White
    Write-Host "  Project: $Project" -ForegroundColor White
    Write-Host "  Tests: $(if ($SkipTests) { 'Skipped' } else { 'Executed' })" -ForegroundColor White
    Write-Host "  Package: $(if ($Package) { 'Created' } else { 'Skipped' })" -ForegroundColor White
    
    if ($Package) {
        Write-Host "  Artifacts:" -ForegroundColor White
        if (Test-Path $PublishDir) {
            Get-ChildItem -Path $PublishDir -Directory | ForEach-Object {
                $size = (Get-ChildItem -Path $_.FullName -Recurse | Measure-Object -Property Length -Sum).Sum
                $sizeStr = if ($size -gt 1MB) { "{0:N1} MB" -f ($size / 1MB) } else { "{0:N0} KB" -f ($size / 1KB) }
                Write-Host "    $($_.Name): $sizeStr" -ForegroundColor Gray
            }
        }
    }
}

# Main execution
try {
    $stopwatch = [System.Diagnostics.Stopwatch]::StartNew()
    
    Write-BuildInfo "Starting AIMX DotnetServices build..."
    Write-BuildInfo "Configuration: $Configuration, Runtime: $Runtime, Project: $Project"
    
    Test-Prerequisites
    Initialize-BuildEnvironment
    Invoke-Clean
    Invoke-Restore
    Invoke-Build
    Invoke-Tests
    Invoke-Publish
    
    $stopwatch.Stop()
    
    Write-BuildSuccess "Build completed successfully in $($stopwatch.Elapsed.TotalSeconds.ToString('F1')) seconds."
    Show-BuildSummary
}
catch {
    Write-BuildError "Build failed: $($_.Exception.Message)"
    exit 1
}
finally {
    # Cleanup
    $env:DOTNET_CLI_TELEMETRY_OPTOUT = $null
    $env:DOTNET_SKIP_FIRST_TIME_EXPERIENCE = $null
}
