[07/30/2025 09:00:23] Request: get me the replication status of the current domain controller
Command: Get-ADReplicationPartnerHealth -Identity "DomainController" -Server "PDC Emulator" -Credential (Get-Credential!


[07/30/2025 09:00:59] Request: get user information for user rizhang
Command: Get-ADUser "Riz!hang!@example!!c!0m" -Properties *


[07/30/2025 09:01:11] Request: get user information for user rizhang
Command: Get-ADUser "Riz!hang!@example!!c!0m" -Properties *


[07/30/2025 09:01:30] Request: get dns information for the domain
Command: `(Get-ADUser * -Filter 'Enabled -!=$false!')`


