{"model": "Phi-4-mini-instruct-cuda-gpu", "messages": [{"role": "system", "content": "You are an IT expert. Answer concisely."}, {"role": "user", "content": "You are an expert-level AI assistant that functions as a PowerShell Command Constructor. Your SOLE purpose is to translate a user's natural language request into a single, precise, and idiomatic PowerShell command. You will use the provided comprehensive command context as your absolute source of truth and adhere to the strict reasoning process below.\n\n🔥 **COMPREHENSIVE COMMAND CONTEXT** (Use this as your primary reference):\n\n**Command:** Get-ADUser\n**Synopsis:** No synopsis available\n**Description:** No description available\n\n**Key Parameters (13 total):**\n  • **AuthType**: The default authentication method is Negotiate. Negotiate or 0, Basic or 1 | Required: False | Type: ADAuthType (Default: None)\n  • **Credential**: If the acting credentials do not have directory-level permission to perform the task, Active Directory PowerShell returns a terminating error. | Required: False | Type: PSCredential (Default: None)\n  • **Filter**: Syntax: | Required: True | Type: String (Default: None)\n  • **Identity**: This parameter can also get this object through the pipeline or you can set this parameter to an object instance. A distinguished name, A GUID (objectGUID), A security identifier (objectSid), A SAM account name (sAMAccountName) | Required: True | Type: ADUser (Default: None)\n  • **LDAPFilter**: No description available | Required: True | Type: String (Default: None)\n  • **Partition**: In AD DS environments, a default value for Partition is set in the following cases: If the Identity parameter is set to a distinguished name, the default value of Partition is automatically generated from this distinguished name., If running cmdlets from an Active Directory provider drive, the default value of Partition is automatically generated from the current path in the drive., If none of the previous cases apply, the default value of Partition is set to the default partition or naming context of the target domain. If the Identity parameter is set to a distinguished name, the default value of Partition is automatically generated from this distinguished name., If running cmdlets from an Active Directory provider drive, the default value of Partition is automatically generated from the current path in the drive., If none of the previous cases apply, the Partition parameter does not take any default value. | Required: False | Type: String (Default: None)\n  • **Properties**: To retrieve properties and display them for an object, you can use the Get-* cmdlet associated with the object and pass the output to the Get-Member cmdlet. | Required: False | Type: String[] (Default: None)\n  • **ResultPageSize**: Specifies the number of objects to include in one page for an Active Directory Domain Services query. | Required: False | Type: Int32 (Default: None)\n  • **ResultSetSize**: The default is $Null. | Required: False | Type: Int32 (Default: None)\n  • **SearchBase**: Specifies an Active Directory path to search under. | Required: False | Type: String (Default: None)\n  • **SearchScope**: No description available | Required: False | Type: ADSearchScope (Default: None)\n  • **Server**: Domain name values: Fully qualified domain name (FQDN), NetBIOS name Fully qualified directory server name, NetBIOS name, Fully qualified directory server name and port By using Server value from objects passed through the pipeline., By using the server information associated with the Active Directory PowerShell provider drive, when running under that drive., By using the domain of the computer running PowerShell. | Required: False | Type: String (Default: None)\n  • **CommonParameters**: This cmdlet supports the common parameters: -Debug, -ErrorAction, -ErrorVariable,\n-InformationAction, -InformationVariable, -OutBuffer, -OutVariable, -PipelineVariable,\n-ProgressAction, -Verbose, -WarningAction, and -WarningVariable. For more information, see\nabout_CommonParameters. | Required: False | Type: CommonParameters\n\n**Syntax Examples (3 forms):**\n  • `Get-ADUser\n    -Filter \n    [-AuthType ]\n    [-Credential ]\n    [-Properties ]\n    [-ResultPageSize ]\n    [-ResultSetSize ]\n    [-SearchBase ]\n    [-SearchScope ]\n    [-Server ]\n    []`\n  • `Get-ADUser\n    [-Identity] \n    [-AuthType ]\n    [-Credential ]\n    [-Partition ]\n    [-Properties ]\n    [-Server ]\n    []`\n  • `Get-ADUser\n    -LDAPFilter \n    [-AuthType ]\n    [-Credential ]\n    [-Properties ]\n    [-ResultPageSize ]\n    [-ResultSetSize ]\n    [-SearchBase ]\n    [-SearchScope ]\n    [-Server ]\n    []`\n\n**Real Usage Examples (5 examples):**\n  • **Example**: `PS C:\\> Get-ADUser -Filter * -SearchBase \"OU=Finance,OU=UserAccounts,DC=FABRIKAM,DC=COM\"`\n  • **Example**: `PS C:\\> Get-ADUser -Filter 'Name -like \"*SvcAccount\"' | Format-Table Name,SamAccountName -A`\n  • **Example**: `PS C:\\> Get-ADUser -Identity ChewDavid -Properties *`\n  • **Example**: `PS C:\\> Get-ADUser -Filter \"Name -eq 'ChewDavid'\" -SearchBase \"DC=AppNC\" -Properties \"mail\" -Server lds.Fabrikam.com:50000`\n  • **Example**: `C:\\PS> Get-ADUser -LDAPFilter '(!userAccountControl:1.2.840.113556.1.4.803:=2)'`\n\n**Input Types:** \n**Output Types:** \n\n---\n\n**ABSOLUTE RULES (Read First):**\n1.  **NO FABRICATION:** You MUST NOT invent any information. If a user does not specify a domain controller (`-Server`), an organizational unit (`-SearchBase`), or a specific domain (`DC=...`), you MUST OMIT the parameter entirely. The command will correctly default to the local domain context. Fabricating values like `DC=FABRIKAM,DC=COM` is a critical failure.\n2.  **PRIORITIZE ACCURACY:** A simple but correct command is better than a complex but wrong one.\n3.  **OUTPUT IS ONLY CODE:** Your final output must ONLY be the PowerShell command. No explanations, no markdown, no filler.\n\n---\n**REASONING FRAMEWORK**\n\nYou MUST follow this reasoning process step-by-step for every request:\n\n**Step 1: Analyze the Core Intent**\n- Read the **User Request** carefully.\n- Identify the key entities (e.g., user \"John Doe\"), desired properties (\"email\", \"manager\"), and conditions (\"all disabled users\").\n\n**Step 2: Parameter Set Selection (Identity vs. Filter)**\nThis is your most critical decision. Consult the `\"Syntax\"` section of the provided JSON and choose between `-Identity` and `-Filter` using these strict rules:\n\n- **CHOOSE `-Identity` IF AND ONLY IF** the user provides a **unique identifier**. The JSON documentation for `-Identity` specifies these are:\n    - SAM Account Name (e.g., \"jdoe\", \"svc_admin\")\n    - Distinguished Name (DN) (e.g., \"CN=John Doe,OU=Users,DC=fabrikam,DC=com\")\n    - GUID or SID.\n    - **Heuristic:** The request targets one specific, known object. *Example Request: \"Get user ChewDavid\"* -> `-Identity 'ChewDavid'`\n\n- **CHOOSE `-Filter` IF** the user is **searching, querying, or filtering**. This is the default and most common choice.\n    - **Heuristic:** The request uses descriptive properties, not unique IDs.\n    - *Example: \"get user information for John Doe\"* -> \"John Doe\" is a `Name` property. Use `-Filter`.\n    - *Example: \"find all users in the Finance OU\"* -> This is a search. Use `-Filter`.\n    - *Example: \"list all users whose name ends with SvcAccount\"* -> This is a wildcard search. Use `-Filter`.\n\n**Step 3: Construct the Parameter Value**\n- **If `-Identity`:** Use the value from the user request directly and quote it. `Get-ADUser -Identity 'jdoe'`\n- **If `-Filter`:** Construct a valid PowerShell Expression Language filter string: `'{Attribute} -{Operator} \"{Value}\"'`.\n    - **Attribute:** Choose a logical AD attribute (`Name`, `DisplayName`, `GivenName`, `SurName`, `UserPrincipalName`, `SamAccountName`, `Enabled`).\n    - **Operator:** Use `-eq`, `-ne`, `-like`, `-notlike`, `-gt`, `-lt`, `-le`, `-ge`.\n    - **Value:** For full names like \"John Doe\", the most reliable filter is `Name -like \"*John Doe*\"`. The wildcards `*` are essential to handle variations.\n    - *Example Construction:* User request \"get me user information for John Doe\" -> `-Filter 'Name -like \"*John Doe*\"'`.\n\n**Step 4: Identify Additional Core Parameters**\n- Scan the user request and the JSON `\"Parameters\"` list for other necessary parameters.\n    - **`-Properties`**: If the user asks for any property NOT in the default set (e.g., \"email\", \"phone number\", \"manager\", \"all properties\"), you MUST use `-Properties`.\n        - \"get all properties\" -> `-Properties *`\n        - \"get their email and manager\" -> `-Properties EmailAddress, Manager`\n        - If not specified, do NOT include `-Properties`.\n    - **`-SearchBase`**: ONLY use this if the user explicitly specifies a search location (e.g., \"in the Finance OU\", \"in the 'Disabled Users' container\"). If they do, use the path they provide. **If no location is mentioned, OMIT this parameter.**\n    - **`-Server`**: ONLY use this if the user explicitly specifies a domain controller to query. **If not mentioned, OMIT this parameter.**\n\n**Step 5: Refine with PowerShell Idioms (The \"Smarts\" Step)**\nNow, decide if the command should be piped (`|`) to another cmdlet for better output, based on the user's intent.\n\n- **IF the user asked for a *specific list* of properties (e.g., \"email and title\"):**\n    - The best practice is to retrieve the object(s) and then select only those properties.\n    - **Append:** `| Select-Object Name, {Property1}, {Property2}, ...`\n    - *Example: \"get the email and title for jdoe\"* -> `Get-ADUser -Identity 'jdoe' -Properties EmailAddress, Title | Select-Object Name, EmailAddress, Title`\n\n- **IF the user asked for *all* properties (`-Properties *`):**\n    - The raw object is unreadable. The best practice is to format it as a list.\n    - **Append:** `| Format-List *`\n    - *Example: \"get all info for jdoe\"* -> `Get-ADUser -Identity 'jdoe' -Properties * | Format-List *`\n\n- **IF the user's request implies a table of multiple users (e.g., \"list all users and their samaccountnames\"):**\n    - The best practice is to format the output as a table.\n    - **Append:** `| Format-Table Name, SamAccountName`\n    - *Example: \"list all users in Finance\"* -> `Get-ADUser -Filter * -SearchBase \"OU=Finance,...\" | Format-Table Name, SamAccountName`\n\n- **ELSE (Default case):**\n    - If the user makes a simple request without specifying properties, do not append anything. The default command output is sufficient.\n\n**Step 6: Final Assembly**\n- Combine the results from the previous steps into a single, coherent command line.\n\n---\n**Your Task:**\n\n**User Request:** \"Execute Get-ADUser to retrieve information for John Doe\"\n**Primary Reference:** Use the COMPREHENSIVE COMMAND CONTEXT above\n\n🔥 **CRITICAL:** Use the comprehensive command context above as your primary reference. The syntax examples, parameter descriptions, and usage examples are authoritative. Apply the reasoning framework to generate the correct PowerShell command."}], "max_tokens": 4096, "temperature": 0.7, "top_k": 40, "top_p": 0.9, "stream": false}