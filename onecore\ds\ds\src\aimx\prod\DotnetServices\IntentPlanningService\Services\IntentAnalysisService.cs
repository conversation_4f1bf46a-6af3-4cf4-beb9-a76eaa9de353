/*++

Copyright (c) Microsoft Corporation. All rights reserved.

Module Name:

    IntentAnalysisService.cs

Abstract:

    Service for analyzing user intent and extracting goals from natural language requests.
    Implements command-first approach with PowerShell-aware intent classification.

Author:

    <PERSON><PERSON><PERSON> (rizhang) 07/29/2025

Environment:

    User mode only.

--*/

using IntentPlanningService.Configuration;
using IntentPlanningService.Models;
using Microsoft.Extensions.AI;
using Microsoft.SemanticKernel;
using System.Text.Json;
using System.Text.Json.Serialization;
using System.Text.RegularExpressions;
using AimxShared.Constants;

namespace IntentPlanningService.Services;

/// <summary>
/// LLM API response models
/// </summary>
public class LlmResponse
{
    public List<LlmChoice> Choices { get; set; } = new();
}

public class LlmChoice
{
    public LlmMessage Message { get; set; } = new();
}

public class LlmMessage
{
    public string Content { get; set; } = string.Empty;
}

/// <summary>
/// Service for analyzing user intent using SLM (Small Language Model)
/// </summary>
public class IntentAnalysisService
{
    private readonly ILogger<IntentAnalysisService> _logger;
    private readonly IntentPlanningConfiguration _config;
    private readonly IEmbeddingGenerator<string, Embedding<float>> _embeddingService;
    private readonly Kernel _kernel;
    private readonly Dictionary<string, UserGoal> _goalCache;
    private readonly SemaphoreSlim _semaphore;
    private readonly IHttpClientFactory _httpClientFactory;

    public IntentAnalysisService(
        ILogger<IntentAnalysisService> logger,
        IntentPlanningConfiguration config,
        IEmbeddingGenerator<string, Embedding<float>> embeddingService,
        Kernel kernel,
        IHttpClientFactory httpClientFactory)
    {
        _logger = logger;
        _config = config;
        _embeddingService = embeddingService;
        _kernel = kernel;
        _goalCache = new Dictionary<string, UserGoal>();
        _semaphore = new SemaphoreSlim(_config.Service.MaxConcurrentRequests, _config.Service.MaxConcurrentRequests);
        _httpClientFactory = httpClientFactory;
    }

    /// <summary>
    /// Analyze user input to extract intent and goals
    /// </summary>
    public async Task<UserGoal> AnalyzeIntentAsync(UserRequest request)
    {
        await _semaphore.WaitAsync();
        try
        {
            _logger.LogInformation("=== STARTING INTENT ANALYSIS ===");
            _logger.LogInformation("Request ID: {RequestId}", request.RequestId);
            _logger.LogInformation("User Input: '{UserInput}'", request.UserInput);
            _logger.LogInformation("User ID: {UserId}", request.UserId);
            _logger.LogInformation("Priority: {Priority}", request.Priority);
            _logger.LogInformation("Environment: {Environment}", request.Environment);

            var stopwatch = System.Diagnostics.Stopwatch.StartNew();

            // Check cache first if enabled
            if (_config.Slm.EnableCaching)
            {
                _logger.LogInformation("STEP 0: Checking cache for existing analysis");
                var cacheKey = GenerateCacheKey(request.UserInput);
                if (_goalCache.TryGetValue(cacheKey, out var cachedGoal))
                {
                    _logger.LogInformation("Retrieved goal from cache for request {RequestId}", request.RequestId);
                    _logger.LogInformation("=== INTENT ANALYSIS COMPLETED (CACHED) ===");
                    return cachedGoal;
                }
                _logger.LogInformation("No cached result found");
            }

            // Step 1: Scope filtering - quick check if this is an IT operation request
            _logger.LogInformation("STEP 1: AI-Powered Scope Filtering");
            var isITOperation = await IsITOperationRequestAsync(request.UserInput);
            _logger.LogInformation("Scope filtering result: {IsInScope}", isITOperation);

            if (!isITOperation)
            {
                _logger.LogWarning("Request {RequestId} does not appear to be an IT operation", request.RequestId);
                _logger.LogInformation("=== INTENT ANALYSIS COMPLETED (OUT OF SCOPE) ===");
                return CreateErrorGoal("Input does not appear to be an IT operation request", 0.1);
            }

            _logger.LogInformation("Request is in scope for IT operations");

            // Step 2: Intent classification using SLM
            _logger.LogInformation("STEP 2: Intent Classification using RAG + LLM");
            var intentCategory = await ClassifyIntentAsync(request.UserInput);
            _logger.LogInformation("Classified intent as '{Category}' for request {RequestId}", intentCategory, request.RequestId);

            // Step 3: Goal extraction using SLM
            _logger.LogInformation("STEP 3: Goal Extraction using LLM");
            var userGoal = await ExtractGoalAsync(request, intentCategory);
            _logger.LogInformation("Goal extraction completed with confidence {Confidence}", userGoal.ExtractionConfidence);

            // Step 4: Context enrichment
            if (_config.IntentAnalysis.EnableContextEnrichment)
            {
                await EnrichContextAsync(userGoal, request);
            }

            // Step 5: Confidence validation
            if (userGoal.ExtractionConfidence < _config.IntentAnalysis.MinConfidenceThreshold)
            {
                _logger.LogWarning("Low confidence ({Confidence}) for request {RequestId}, attempting fallback", 
                    userGoal.ExtractionConfidence, request.RequestId);

                // TODO: Implement AI-driven fallback mechanism
                _logger.LogWarning("Goal extraction failed for request {RequestId}, no fallback implemented", request.RequestId);
            }

            stopwatch.Stop();
            _logger.LogInformation("Intent analysis completed for request {RequestId} in {ElapsedMs}ms with confidence {Confidence}", 
                request.RequestId, stopwatch.ElapsedMilliseconds, userGoal.ExtractionConfidence);

            // Cache the result if enabled
            if (_config.Slm.EnableCaching && userGoal.ExtractionConfidence >= _config.IntentAnalysis.MinConfidenceThreshold)
            {
                var cacheKey = GenerateCacheKey(request.UserInput);
                _goalCache[cacheKey] = userGoal;
                
                // Simple cache cleanup - remove oldest entries if cache is too large
                if (_goalCache.Count > 1000)
                {
                    var oldestKey = _goalCache.Keys.First();
                    _goalCache.Remove(oldestKey);
                }
            }

            return userGoal;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error analyzing intent for request {RequestId}", request.RequestId);
            return CreateErrorGoal($"Error analyzing intent: {ex.Message}", 0.0);
        }
        finally
        {
            _semaphore.Release();
        }
    }

    /// <summary>
    /// AI-powered scope filtering to determine if input is an IT operation request
    /// </summary>
    private async Task<bool> IsITOperationRequestAsync(string userInput)
    {
        try
        {
            var prompt = $@"Determine if this request is related to IT operations, system administration, or technical support.

Request: ""{userInput}""

Respond with only 'YES' if it's IT-related, or 'NO' if it's not IT-related.

Examples:
- ""Create user account"" → YES
- ""Reset password"" → YES
- ""What's the weather?"" → NO
- ""Book a meeting room"" → NO";

            var response = await CallLlmAsync(prompt);

            // Check if LLM service returned a fallback response due to errors
            if (response.Contains("fallback response") || response.Contains("service unavailable") || response.Contains("service error") || response.Contains("service timeout"))
            {
                _logger.LogWarning("LLM service failed for scope filtering, defaulting to IT operation assumption");
                return true;
            }

            var isITOperation = response.Trim().ToUpperInvariant().StartsWith("YES");

            _logger.LogInformation("LLM scope filtering result for '{Input}': {Result}", userInput, isITOperation ? "IT Operation" : "Non-IT Request");
            return isITOperation;
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "LLM scope filtering failed, defaulting to IT operation assumption");
            return true; // Default to assuming it's an IT operation if LLM fails
        }

        /*
        try
        {
            // Create AI prompt for scope filtering
            var prompt = $@"Is this an IT administration request?
Input: ""{userInput}""

Respond with only: YES or NO";

            var response = await CallLlmAsync(prompt);
            var isITOperation = response.Trim().ToUpperInvariant().StartsWith("YES");

            _logger.LogDebug("Scope filtering result for '{Input}': {Result} - {Response}",
                userInput.Substring(0, Math.Min(50, userInput.Length)), isITOperation, response);

            return isITOperation;
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Error in AI scope filtering, using fallback");

            // Temporary fix: Skip LLM fallback and return true for IT operations
            // TODO: Fix LLM service memory allocation issues
            _logger.LogInformation("Bypassing LLM fallback due to service issues, assuming IT operation");
            return true; // Default to true to avoid false negatives
        }
        */
    }

    /// <summary>
    /// REDESIGNED: Command-first intent classification that recognizes direct PowerShell operations
    /// </summary>
    private async Task<string> ClassifyIntentAsync(string userInput)
    {
        try
        {
            // STEP 1: Check if this is a direct PowerShell command request
            var directCommand = await TryIdentifyDirectCommandAsync(userInput);
            if (!string.IsNullOrEmpty(directCommand))
            {
                _logger.LogInformation("🎯 DIRECT COMMAND identified: {Command} for input: {Input}", directCommand, userInput);
                return "direct_command";
            }

            // STEP 2: Use improved classification with PowerShell-aware categories
            var prompt = $@"You are an expert PowerShell and Active Directory specialist. Classify this IT request based on the PRIMARY PowerShell domain it involves.

CLASSIFICATION RULES:
1. If the request can be fulfilled by a SINGLE PowerShell cmdlet, classify as 'direct_command'
2. Otherwise, classify by the PRIMARY AD/PowerShell domain:

Categories:
- direct_command: Single cmdlet operations (Get-ADUser, Get-ADDefaultDomainPasswordPolicy, etc.)
- user_operations: User account management (New-ADUser, Set-ADUser, Remove-ADUser, etc.)
- group_operations: Group management (New-ADGroup, Add-ADGroupMember, etc.)
- computer_operations: Computer account management (New-ADComputer, Get-ADComputer, etc.)
- security_policy: Password policies, security settings (Get-ADDefaultDomainPasswordPolicy, etc.)
- system_query: Information retrieval that requires multiple steps
- complex_workflow: Multi-step operations requiring coordination

EXAMPLES:
- ""get default domain password policy"" → direct_command (Get-ADDefaultDomainPasswordPolicy)
- ""show me user john doe"" → direct_command (Get-ADUser)
- ""create user account for sarah"" → user_operations (may need multiple steps)
- ""get all users in finance group"" → direct_command (Get-ADGroupMember)
- ""set up new employee onboarding"" → complex_workflow (multiple cmdlets needed)

User Request: ""{userInput}""

Respond with only the category name.";

            var response = await CallLlmAsync(prompt);
            var category = response.Trim().ToLowerInvariant();

            var validCategories = new[] { "direct_command", "user_operations", "group_operations", "computer_operations", "security_policy", "system_query", "complex_workflow" };

            if (validCategories.Contains(category))
            {
                _logger.LogInformation("Classified intent '{Input}' as '{Category}'", userInput.Substring(0, Math.Min(50, userInput.Length)), category);
                return category;
            }

            _logger.LogWarning("Invalid category '{Category}', defaulting to system_query", category);
            return "system_query";
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in intent classification");
            return "system_query";
        }
    }

    /// <summary>
    /// Try to identify if the user request maps directly to a single PowerShell cmdlet
    /// </summary>
    private async Task<string?> TryIdentifyDirectCommandAsync(string userInput)
    {
        try
        {
            var prompt = $@"You are a PowerShell expert. Analyze if this user request can be fulfilled by a SINGLE PowerShell cmdlet.

DIRECT COMMAND PATTERNS:
- ""get default domain password policy"" → Get-ADDefaultDomainPasswordPolicy
- ""show user john doe"" → Get-ADUser
- ""get all users"" → Get-ADUser
- ""list domain controllers"" → Get-ADDomainController
- ""show group members of finance"" → Get-ADGroupMember
- ""get computer info for PC123"" → Get-ADComputer
- ""show domain info"" → Get-ADDomain
- ""list all groups"" → Get-ADGroup
- ""get forest info"" → Get-ADForest
- ""show replication status"" → Get-ADReplicationFailure

RULES:
1. If the request can be fulfilled by ONE cmdlet with parameters, return the cmdlet name
2. If it requires multiple steps or complex logic, return 'COMPLEX'
3. If unclear or ambiguous, return 'UNCLEAR'

User Request: ""{userInput}""

Respond with:
- The exact PowerShell cmdlet name (e.g., 'Get-ADDefaultDomainPasswordPolicy')
- 'COMPLEX' if multiple steps needed
- 'UNCLEAR' if ambiguous

Response:";

            var response = await CallLlmAsync(prompt);
            var result = response.Trim();

            // Validate if it's a real PowerShell cmdlet
            if (result.StartsWith("Get-AD") || result.StartsWith("Set-AD") || result.StartsWith("New-AD") ||
                result.StartsWith("Remove-AD") || result.StartsWith("Add-AD") || result.StartsWith("Search-AD") ||
                result.StartsWith("Enable-AD") || result.StartsWith("Disable-AD") || result.StartsWith("Unlock-AD"))
            {
                _logger.LogInformation("🎯 Direct command identified: {Command}", result);
                return result;
            }

            return null;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error identifying direct command");
            return null;
        }
    }

    /// <summary>
    /// AI-powered goal extraction using LLM
    /// </summary>
    private async Task<UserGoal> ExtractGoalAsync(UserRequest request, string intentCategory)
    {
        try
        {
            // REDESIGNED: Handle direct commands with simplified goal extraction
            if (intentCategory == "direct_command")
            {
                return await ExtractDirectCommandGoalAsync(request);
            }

            // For complex workflows, use enhanced goal extraction
            var prompt = $@"You are a PowerShell and Active Directory expert. Extract the user's goal with focus on PowerShell implementation.

Category: {intentCategory}
User Request: ""{request.UserInput}""

GOAL EXTRACTION RULES:
1. Focus on the PRIMARY PowerShell operation needed
2. Identify specific entities (usernames, group names, computer names)
3. Determine if this is a single-step or multi-step operation
4. Consider security and business constraints

EXAMPLES:
- ""create user account for john smith"" → Goal: Create new AD user account, Steps: New-ADUser with required parameters
- ""add sarah to finance group"" → Goal: Add user to AD group, Steps: Add-ADGroupMember
- ""reset password for bob.jones"" → Goal: Reset user password, Steps: Set-ADAccountPassword

Respond in JSON format:
{{
  ""primaryObjective"": ""Clear, PowerShell-focused description"",
  ""successCriteria"": ""Measurable outcome"",
  ""estimatedSteps"": 1,
  ""complexity"": ""simple|moderate|complex"",
  ""requiredEntities"": [""entity1"", ""entity2""],
  ""suggestedApproach"": ""Brief description of PowerShell approach""
}}";

            var response = await CallLlmAsync(prompt);
            var goalData = ParseLlmJsonResponse(response);

            var userGoal = new UserGoal
            {
                PrimaryObjective = goalData.GetValueOrDefault("primaryObjective", "")?.ToString() ?? "",
                SuccessCriteria = goalData.GetValueOrDefault("successCriteria", "")?.ToString() ?? "",
                Constraints = new List<string>(), // Simplified for now
                UrgencyLevel = request.Priority,
                ExtractionConfidence = 0.9, // Higher confidence with improved approach
                ExtractionMethod = "enhanced_powershell_focused"
            };

            // Enhanced context for PowerShell operations
            userGoal.Context["requestId"] = request.RequestId;
            userGoal.Context["userId"] = request.UserId;
            userGoal.Context["intentCategory"] = intentCategory;
            userGoal.Context["environment"] = request.Environment;
            userGoal.Context["priority"] = request.Priority;
            userGoal.Context["estimatedSteps"] = goalData.GetValueOrDefault("estimatedSteps", 1)?.ToString() ?? "1";
            userGoal.Context["complexity"] = goalData.GetValueOrDefault("complexity", "simple")?.ToString() ?? "simple";
            userGoal.Context["requiredEntities"] = string.Join(",", ParseStringArray(goalData.GetValueOrDefault("requiredEntities")));
            userGoal.Context["suggestedApproach"] = goalData.GetValueOrDefault("suggestedApproach", "")?.ToString() ?? "";
            userGoal.Context["extractionMethod"] = "llm_only";

            _logger.LogDebug("AI extracted goal for request {RequestId}: {Objective} (Confidence: {Confidence})",
                request.RequestId, userGoal.PrimaryObjective, userGoal.ExtractionConfidence);

            return userGoal;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in AI goal extraction");
            return await FallbackToRulesBasedExtraction(request, intentCategory);
        }
    }

    /// <summary>
    /// Extract goal for direct PowerShell command operations
    /// </summary>
    private async Task<UserGoal> ExtractDirectCommandGoalAsync(UserRequest request)
    {
        try
        {
            var prompt = $@"You are a PowerShell expert. This request maps to a SINGLE PowerShell cmdlet. Extract the goal with focus on the direct command execution.

User Request: ""{request.UserInput}""

DIRECT COMMAND EXAMPLES:
- ""get default domain password policy"" → Goal: Retrieve domain password policy settings using Get-ADDefaultDomainPasswordPolicy
- ""show user john doe"" → Goal: Display user account information using Get-ADUser
- ""list all domain controllers"" → Goal: List domain controllers using Get-ADDomainController

Extract the goal for this direct command operation:

Respond in JSON format:
{{
  ""primaryObjective"": ""Execute [specific cmdlet] to [specific purpose]"",
  ""successCriteria"": ""Command executes successfully and returns expected data"",
  ""suggestedCommand"": ""PowerShell cmdlet name"",
  ""parameters"": [""param1"", ""param2""],
  ""outputType"": ""information|configuration|status""
}}";

            var response = await CallLlmAsync(prompt);
            var goalData = ParseLlmJsonResponse(response);

            var userGoal = new UserGoal
            {
                PrimaryObjective = goalData.GetValueOrDefault("primaryObjective", "")?.ToString() ?? $"Execute PowerShell command for: {request.UserInput}",
                SuccessCriteria = goalData.GetValueOrDefault("successCriteria", "")?.ToString() ?? "Command executes successfully",
                Constraints = new List<string>(),
                UrgencyLevel = request.Priority,
                ExtractionConfidence = 0.95, // High confidence for direct commands
                ExtractionMethod = "direct_command_extraction"
            };

            // Add direct command context
            userGoal.Context["requestId"] = request.RequestId;
            userGoal.Context["userId"] = request.UserId;
            userGoal.Context["intentCategory"] = "direct_command";
            userGoal.Context["environment"] = request.Environment;
            userGoal.Context["priority"] = request.Priority;
            userGoal.Context["suggestedCommand"] = goalData.GetValueOrDefault("suggestedCommand", "")?.ToString() ?? "";
            userGoal.Context["parameters"] = string.Join(",", ParseStringArray(goalData.GetValueOrDefault("parameters")));
            userGoal.Context["outputType"] = goalData.GetValueOrDefault("outputType", "information")?.ToString() ?? "information";
            userGoal.Context["estimatedSteps"] = "1";
            userGoal.Context["complexity"] = "simple";

            _logger.LogInformation("Direct command goal extracted: {Objective}", userGoal.PrimaryObjective);
            return userGoal;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error extracting direct command goal");

            // Fallback for direct commands
            return new UserGoal
            {
                PrimaryObjective = $"Execute PowerShell command for: {request.UserInput}",
                SuccessCriteria = "Command executes successfully",
                Constraints = new List<string>(),
                UrgencyLevel = request.Priority,
                ExtractionConfidence = 0.7,
                ExtractionMethod = "direct_command_fallback",
                Context = new Dictionary<string, string>
                {
                    ["intentCategory"] = "direct_command",
                    ["estimatedSteps"] = "1",
                    ["complexity"] = "simple"
                }
            };
        }
    }

    /// <summary>
    /// Fallback to rules-based goal extraction
    /// </summary>
    private Task<UserGoal> FallbackToRulesBasedExtraction(UserRequest request, string intentCategory)
    {
        _logger.LogInformation("Using rules-based fallback for goal extraction");

        var userGoal = new UserGoal
        {
            ExtractionMethod = "rule_based",
            ExtractionConfidence = 0.6 // Lower confidence for rule-based
        };

        // Simple rule-based extraction based on keywords and patterns
        var input = request.UserInput.ToLowerInvariant();

        // Extract primary objective based on action verbs
        if (input.Contains("create") || input.Contains("add") || input.Contains("new"))
        {
            userGoal.PrimaryObjective = $"Create or add new {intentCategory.Replace("_", " ")} resource";
        }
        else if (input.Contains("delete") || input.Contains("remove"))
        {
            userGoal.PrimaryObjective = $"Delete or remove {intentCategory.Replace("_", " ")} resource";
        }
        else if (input.Contains("modify") || input.Contains("update") || input.Contains("change"))
        {
            userGoal.PrimaryObjective = $"Modify or update {intentCategory.Replace("_", " ")} resource";
        }
        else if (input.Contains("find") || input.Contains("search") || input.Contains("list"))
        {
            userGoal.PrimaryObjective = $"Find or search {intentCategory.Replace("_", " ")} resources";
        }
        else
        {
            userGoal.PrimaryObjective = $"Perform {intentCategory.Replace("_", " ")} operation";
        }

        userGoal.SuccessCriteria = "Operation completed successfully without errors";
        userGoal.UrgencyLevel = request.Priority;

        // Add context
        userGoal.Context["requestId"] = request.RequestId;
        userGoal.Context["userId"] = request.UserId;
        userGoal.Context["intentCategory"] = intentCategory;
        userGoal.Context["environment"] = request.Environment;
        userGoal.Context["fallbackReason"] = "SLM confidence too low";

        return Task.FromResult(userGoal);
    }

    /// <summary>
    /// Enrich goal context with additional information
    /// </summary>
    private Task EnrichContextAsync(UserGoal userGoal, UserRequest request)
    {
        try
        {
            // Add timestamp and tracking information
            userGoal.Context["extractedAt"] = DateTime.UtcNow.ToString("O");
            userGoal.Context["originalInput"] = request.UserInput;

            // Extract entities (users, groups, computers) mentioned in the request
            var entities = ExtractEntities(request.UserInput);
            if (entities.Any())
            {
                userGoal.Context["mentionedEntities"] = string.Join(",", entities);
            }

            // Add environment-specific context
            if (request.Environment == "production")
            {
                userGoal.Constraints.Add("Requires extra caution in production environment");
            }

            // Add priority-based constraints
            if (request.Priority == "high" || request.Priority == "critical")
            {
                userGoal.Constraints.Add("High priority - expedited processing required");
            }
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Error enriching context for goal {GoalId}", userGoal.GoalId);
        }
        
        return Task.CompletedTask;
    }

    #region Helper Methods

    /// <summary>
    /// Query RAG database for scope filtering examples
    /// </summary>
    private async Task<List<RagScopeResult>> QueryRagForScopeExamplesAsync(string userInput)
    {
        try
        {
            var ragRequest = new
            {
                query = userInput,
                type = "scope_examples",
                maxResults = 5,
                minSimilarity = 0.7
            };

            using var httpClient = _httpClientFactory.CreateClient("RagService");

            // Use the correct PowerShell command search endpoint
            var encodedQuery = Uri.EscapeDataString(ragRequest.query);
            var searchUrl = $"{AimxConstants.ApiEndpoints.PowerShellCommandSearch}?query={encodedQuery}&limit={ragRequest.maxResults}";

            var response = await httpClient.GetAsync(searchUrl);

            if (response.IsSuccessStatusCode)
            {
                var responseContent = await response.Content.ReadAsStringAsync();
                var commandResults = JsonSerializer.Deserialize<List<PowerShellCommandResult>>(responseContent);

                return commandResults?.Select(cmd => new RagScopeResult
                {
                    Content = cmd.FullText ?? cmd.CommandName,
                    Confidence = cmd.Score,
                    IsITOperation = true // PowerShell commands are always IT operations
                }).ToList() ?? new List<RagScopeResult>();
            }

            return new List<RagScopeResult>();
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Error querying RAG for scope examples");
            return new List<RagScopeResult>();
        }
    }

    /// <summary>
    /// Query RAG database for intent classification examples
    /// </summary>
    private async Task<List<RagIntentResult>> QueryRagForIntentExamplesAsync(string userInput)
    {
        try
        {
            var ragRequest = new
            {
                query = userInput,
                type = "intent_examples",
                maxResults = 10,
                minSimilarity = 0.6
            };

            using var httpClient = _httpClientFactory.CreateClient("RagService");

            // Use the correct PowerShell command search endpoint
            var encodedQuery = Uri.EscapeDataString(ragRequest.query);
            var searchUrl = $"{AimxConstants.ApiEndpoints.PowerShellCommandSearch}?query={encodedQuery}&limit={ragRequest.maxResults}";

            var response = await httpClient.GetAsync(searchUrl);

            if (response.IsSuccessStatusCode)
            {
                var responseContent = await response.Content.ReadAsStringAsync();
                var commandResults = JsonSerializer.Deserialize<List<PowerShellCommandResult>>(responseContent);

                return commandResults?.Select(cmd => new RagIntentResult
                {
                    Query = cmd.CommandName,
                    Category = "powershell_command", // PowerShell commands have their own category
                    Confidence = cmd.Score
                }).ToList() ?? new List<RagIntentResult>();
            }

            return new List<RagIntentResult>();
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Error querying RAG for intent examples");
            return new List<RagIntentResult>();
        }
    }

    /// <summary>
    /// Query RAG database for goal extraction examples
    /// </summary>
    private async Task<List<RagGoalResult>> QueryRagForGoalExamplesAsync(string userInput, string intentCategory)
    {
        try
        {
            var ragRequest = new
            {
                query = userInput,
                type = "goal_examples",
                category = intentCategory,
                maxResults = 8,
                minSimilarity = 0.6
            };

            using var httpClient = _httpClientFactory.CreateClient("RagService");

            // Use the correct PowerShell command search endpoint
            var encodedQuery = Uri.EscapeDataString(ragRequest.query);
            var searchUrl = $"{AimxConstants.ApiEndpoints.PowerShellCommandSearch}?query={encodedQuery}&limit={ragRequest.maxResults}";

            var response = await httpClient.GetAsync(searchUrl);

            if (response.IsSuccessStatusCode)
            {
                var responseContent = await response.Content.ReadAsStringAsync();
                var commandResults = JsonSerializer.Deserialize<List<PowerShellCommandResult>>(responseContent);

                return commandResults?.Select(cmd => new RagGoalResult
                {
                    Query = cmd.CommandName,
                    ExtractedGoal = $"Execute PowerShell command: {cmd.CommandName}",
                    SuccessCriteria = "Command executes successfully and returns expected results",
                    Constraints = new List<string>(), // PowerShell commands don't have predefined constraints
                    Confidence = cmd.Score
                }).ToList() ?? new List<RagGoalResult>();
            }

            return new List<RagGoalResult>();
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Error querying RAG for goal examples");
            return new List<RagGoalResult>();
        }
    }

    /// <summary>
    /// Call LLM for complex reasoning and analysis
    /// </summary>
    private async Task<string> CallLlmAsync(string prompt)
    {
        try
        {
            using var httpClient = _httpClientFactory.CreateClient();
            httpClient.Timeout = TimeSpan.FromSeconds(AimxConstants.Timeouts.ShortHttpTimeoutSeconds);

            var requestBody = new
            {
                model = AimxConstants.ModelConfig.CurrentLlmModel,
                messages = new[]
                {
                    new { role = "system", content = "You are an IT expert. Answer concisely." },
                    new { role = "user", content = prompt }
                },
                max_tokens = AimxConstants.ModelConfig.LargeMaxTokens,
                temperature = AimxConstants.ModelConfig.HighTemperature,
                top_k = AimxConstants.ModelConfig.TopK,
                top_p = AimxConstants.ModelConfig.TopP,
                stream = false
            };

            var json = JsonSerializer.Serialize(requestBody);
            var content = new StringContent(json, System.Text.Encoding.UTF8, "application/json");

            // 🔍 COMPREHENSIVE LLM PAYLOAD LOGGING
            var timestamp = DateTime.UtcNow.ToString("yyyy-MM-dd_HH-mm-ss-fff");
            var logFileName = $"llm_request_{timestamp}_{Guid.NewGuid().ToString("N")[..8]}.json";
            var logFilePath = Path.Combine("logs", "llm_requests", logFileName);

            try
            {
                Directory.CreateDirectory(Path.GetDirectoryName(logFilePath)!);
                await File.WriteAllTextAsync(logFilePath, json);
                _logger.LogInformation("🔍 LLM REQUEST LOGGED: {LogFile} | Prompt Length: {PromptLength} chars",
                    logFileName, prompt.Length);
            }
            catch (Exception logEx)
            {
                _logger.LogWarning(logEx, "Failed to log LLM request to file");
            }

            // Also log to console for immediate visibility
            _logger.LogInformation("🔍 LLM REQUEST PAYLOAD:\n{Payload}", json);

            var response = await httpClient.PostAsync($"{AimxConstants.ServiceEndpoints.LlmServiceBaseUrl}{AimxConstants.ApiEndpoints.ChatCompletions}", content);

            if (!response.IsSuccessStatusCode)
            {
                _logger.LogError("LLM service returned {StatusCode}: {ReasonPhrase}",
                    response.StatusCode, response.ReasonPhrase);
                throw new HttpRequestException($"LLM service error: {response.StatusCode}");
            }

            var responseContent = await response.Content.ReadAsStringAsync();

            // 🔍 LOG LLM RESPONSE
            _logger.LogInformation("🔍 LLM RESPONSE PAYLOAD:\n{Response}", responseContent);
            _logger.LogDebug("Raw LLM response: {Response}", responseContent);

            var options = new JsonSerializerOptions
            {
                PropertyNameCaseInsensitive = true
            };
            var llmResponse = JsonSerializer.Deserialize<LlmResponse>(responseContent, options);

            var result = llmResponse?.Choices?.FirstOrDefault()?.Message?.Content ?? "";

            _logger.LogDebug("LLM response length: {Length} characters", result.Length);
            _logger.LogDebug("Parsed LLM content: {Content}", result);

            return result;
        }
        catch (TaskCanceledException ex)
        {
            _logger.LogWarning(ex, "LLM service request timed out");
            return "LLM service timeout - using fallback response";
        }
        catch (HttpRequestException ex)
        {
            _logger.LogWarning(ex, "LLM service connection failed");
            return "LLM service unavailable - using fallback response";
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error calling LLM service");
            return "LLM service error - using fallback response";
        }
    }

    private Dictionary<string, object?> ParseLlmJsonResponse(string response)
    {
        try
        {
            // Clean up the response - remove any markdown formatting
            var cleanResponse = response.Trim();
            if (cleanResponse.StartsWith("```json"))
            {
                cleanResponse = cleanResponse.Substring(7);
            }
            if (cleanResponse.EndsWith("```"))
            {
                cleanResponse = cleanResponse.Substring(0, cleanResponse.Length - 3);
            }

            return JsonSerializer.Deserialize<Dictionary<string, object?>>(cleanResponse) ?? new();
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Error parsing SLM JSON response: {Response}", response);
            return new Dictionary<string, object?>();
        }
    }

    private List<string> ParseStringArray(object? value)
    {
        if (value is JsonElement element && element.ValueKind == JsonValueKind.Array)
        {
            return element.EnumerateArray()
                .Where(e => e.ValueKind == JsonValueKind.String)
                .Select(e => e.GetString() ?? "")
                .Where(s => !string.IsNullOrEmpty(s))
                .ToList();
        }
        return new List<string>();
    }





    private List<string> ExtractEntities(string userInput)
    {
        var entities = new List<string>();

        // Simple regex patterns for common entities
        var patterns = new Dictionary<string, string>
        {
            { "username", @"\b[a-zA-Z][a-zA-Z0-9._-]{2,19}\b" },
            { "email", @"\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b" },
            { "computer", @"\b[A-Za-z0-9-]{3,15}\b(?=\s|$)" },
            { "group", @"\b(?:group|grp)[\s_-]?[A-Za-z0-9_-]+\b" }
        };

        foreach (var pattern in patterns)
        {
            var matches = Regex.Matches(userInput, pattern.Value, RegexOptions.IgnoreCase);
            entities.AddRange(matches.Cast<Match>().Select(m => m.Value));
        }

        return entities.Distinct().ToList();
    }

    private string GenerateCacheKey(string userInput)
    {
        // Simple hash-based cache key
        return $"intent_{userInput.GetHashCode():X}";
    }

    private UserGoal CreateErrorGoal(string errorMessage, double confidence)
    {
        return new UserGoal
        {
            PrimaryObjective = "Error in intent analysis",
            SuccessCriteria = "N/A",
            UrgencyLevel = "normal",
            ExtractionConfidence = confidence,
            ExtractionMethod = "error",
            Context = new Dictionary<string, string>
            {
                { "error", errorMessage },
                { "timestamp", DateTime.UtcNow.ToString("O") }
            }
        };
    }



    #endregion
}

/// <summary>
/// RAG result for scope filtering
/// </summary>
public class RagScopeResult
{
    public string Content { get; set; } = string.Empty;
    public double Confidence { get; set; }
    public bool IsITOperation { get; set; }
}

/// <summary>
/// RAG result for intent classification
/// </summary>
public class RagIntentResult
{
    public string Query { get; set; } = string.Empty;
    public string Category { get; set; } = string.Empty;
    public double Confidence { get; set; }
}

/// <summary>
/// RAG result for goal extraction
/// </summary>
public class RagGoalResult
{
    public string Query { get; set; } = string.Empty;
    public string ExtractedGoal { get; set; } = string.Empty;
    public string SuccessCriteria { get; set; } = string.Empty;
    public List<string> Constraints { get; set; } = new();
    public double Confidence { get; set; }
}

/// <summary>
/// RAG query response structure
/// </summary>
public class RagQueryResponse
{
    public List<RagResult> Results { get; set; } = new();
    public int TotalCount { get; set; }
    public double QueryTime { get; set; }
}

/// <summary>
/// Individual RAG result
/// </summary>
public class RagResult
{
    public string Content { get; set; } = string.Empty;
    public double Similarity { get; set; }
    public Dictionary<string, object> Metadata { get; set; } = new();
}


