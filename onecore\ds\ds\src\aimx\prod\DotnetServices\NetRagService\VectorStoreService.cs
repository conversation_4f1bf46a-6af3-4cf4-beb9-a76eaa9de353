using Microsoft.Extensions.Logging;
using Microsoft.Extensions.VectorData;
using Microsoft.SemanticKernel.Connectors.SqliteVec;
using System.Collections.Concurrent;
using System.Numerics.Tensors;
using System.Text.Json;
using System.IO.Compression;

namespace NetRagService;

public class VectorStoreService
{
    private readonly ConcurrentDictionary<string, VectorPoint> _vectors;
    private readonly ILogger<VectorStoreService> _logger;
    private readonly object _lockObject = new();
    private int _vectorSize;
    private readonly string _persistenceFilePath;
    private readonly string _sqliteDbPath;
    private SqliteCollection<string, PowerShellCommand>? _collection;
    private Microsoft.Extensions.AI.IEmbeddingGenerator<string, Microsoft.Extensions.AI.Embedding<float>>? _embeddingGenerator;
    private bool _useSqlite = true;

    public VectorStoreService(ILogger<VectorStoreService> logger, string? persistenceFilePath = null)
    {
        _vectors = new ConcurrentDictionary<string, VectorPoint>();
        _logger = logger;
        _persistenceFilePath = persistenceFilePath ?? Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "vector_store.bin");

        // Use the same base name as persistenceFilePath but with .db extension for SqliteVec
        if (persistenceFilePath != null)
        {
            var directory = Path.GetDirectoryName(persistenceFilePath) ?? AppDomain.CurrentDomain.BaseDirectory;
            var fileNameWithoutExtension = Path.GetFileNameWithoutExtension(persistenceFilePath);
            _sqliteDbPath = Path.Combine(directory, $"{fileNameWithoutExtension}.db");
        }
        else
        {
            _sqliteDbPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "vector_store.db");
        }
    }

    public void SetEmbeddingGenerator(Microsoft.Extensions.AI.IEmbeddingGenerator<string, Microsoft.Extensions.AI.Embedding<float>> embeddingGenerator)
    {
        _embeddingGenerator = embeddingGenerator;
    }

    public async Task InitializeAsync(int vectorSize = 768)
    {
        _vectorSize = vectorSize;

        if (_useSqlite && _embeddingGenerator != null)
        {
            try
            {
                // Initialize SqliteCollection with embedding generator (following the reference pattern)
                var options = new SqliteCollectionOptions
                {
                    EmbeddingGenerator = _embeddingGenerator
                };

                _collection = new SqliteCollection<string, PowerShellCommand>($"Data Source={_sqliteDbPath}", "powershell_commands", options);

                // Create the collection/tables if they don't exist (using the correct method name from migration guide)
                await _collection.EnsureCollectionExistsAsync();

                _logger.LogInformation("SQLite vector store initialized with vector size {VectorSize} at {DbPath}", vectorSize, _sqliteDbPath);
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Failed to initialize SQLite vector store, falling back to in-memory store");
                _useSqlite = false;
                _logger.LogInformation("In-memory vector store initialized with vector size {VectorSize}", vectorSize);
            }
        }
        else
        {
            if (_embeddingGenerator == null)
            {
                _logger.LogWarning("No embedding generator provided, using in-memory store");
            }
            _logger.LogInformation("In-memory vector store initialized with vector size {VectorSize}", vectorSize);
        }
    }

    public async Task UpsertAsync(string id, ReadOnlyMemory<float> embedding, Dictionary<string, object> metadata)
    {
        if (embedding.Length != _vectorSize)
        {
            throw new ArgumentException($"Embedding size {embedding.Length} does not match expected size {_vectorSize}");
        }

        if (_useSqlite && _collection != null)
        {
            try
            {
                // Use SqliteVec
                var command = new PowerShellCommand
                {
                    Id = id,
                    Vector = embedding,
                    CommandName = VectorStoreServiceHelpers.GetMetadataValue(metadata, "commandName", ""),
                    Description = VectorStoreServiceHelpers.GetMetadataValue(metadata, "description", ""),
                    FullText = VectorStoreServiceHelpers.GetMetadataValue(metadata, "fullText", ""),
                    MetadataJson = JsonSerializer.Serialize(metadata),
                    Timestamp = DateTime.UtcNow.ToString("O") // Convert DateTime to ISO string
                };

                await _collection.UpsertAsync(command);
                _logger.LogDebug("Upserted point with ID: {Id} to SqliteVec", id);
                return;
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Failed to upsert to SqliteVec, falling back to in-memory");
                // Fall through to in-memory storage
            }
        }

        // Fallback: in-memory storage
        var point = new VectorPoint
        {
            Id = id,
            Vector = embedding.ToArray(),
            Metadata = new Dictionary<string, object>(metadata),
            Timestamp = DateTime.UtcNow
        };

        _vectors.AddOrUpdate(id, point, (key, oldValue) => point);
        _logger.LogDebug("Upserted point with ID: {Id} to in-memory store", id);
    }

    // New method following the reference pattern - takes string query
    public async Task<List<SearchResult>> SearchAsync(string query, int limit = 3)
    {
        if (_useSqlite && _collection != null)
        {
            try
            {
                var results = new List<SearchResult>();

                // Use the SqliteCollection SearchAsync method (following the reference pattern)
                await foreach (var result in _collection.SearchAsync(query, top: limit))
                {
                    if (result.Record != null)
                    {
                        // Convert PowerShellCommand to SearchResult
                        var metadata = new Dictionary<string, object>();

                        // Deserialize stored metadata
                        if (!string.IsNullOrEmpty(result.Record.MetadataJson))
                        {
                            try
                            {
                                var deserializedMetadata = JsonSerializer.Deserialize<Dictionary<string, object>>(result.Record.MetadataJson);
                                if (deserializedMetadata != null)
                                {
                                    foreach (var kvp in deserializedMetadata)
                                    {
                                        metadata[kvp.Key] = kvp.Value;
                                    }
                                }
                            }
                            catch (Exception ex)
                            {
                                _logger.LogWarning(ex, "Failed to deserialize metadata for record {Id}", result.Record.Id);
                            }
                        }

                        // Add structured metadata (using consistent key names)
                        metadata["commandName"] = result.Record.CommandName;
                        metadata["description"] = result.Record.Description;
                        metadata["fullText"] = result.Record.FullText;

                        results.Add(new SearchResult
                        {
                            Id = result.Record.Id,
                            Score = (float)(result.Score ?? 0.0),
                            Metadata = metadata
                        });
                    }
                }

                _logger.LogDebug("Found {Count} similar points from SQLite vector search", results.Count);
                return results;
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "SQLite vector search failed, falling back to in-memory search");
                // Fall through to in-memory search
            }
        }

        // Fallback: convert query to embedding and use in-memory search
        if (_embeddingGenerator != null)
        {
            try
            {
                var embeddingResults = await _embeddingGenerator.GenerateAsync(new[] { query });
                var embedding = embeddingResults.First();
                return await SearchAsync(embedding.Vector, limit);
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Failed to generate embedding for query, returning empty results");
                return new List<SearchResult>();
            }
        }

        _logger.LogWarning("No embedding generator available for query search");
        return new List<SearchResult>();
    }

    // Original method - takes embedding vector
    public Task<List<SearchResult>> SearchAsync(ReadOnlyMemory<float> queryEmbedding, int limit = 3)
    {
        if (queryEmbedding.Length != _vectorSize)
        {
            throw new ArgumentException($"Query embedding size {queryEmbedding.Length} does not match expected size {_vectorSize}");
        }

        var queryVector = queryEmbedding.ToArray();
        var results = new List<(VectorPoint point, float score)>();

        // Calculate cosine similarity for all vectors
        foreach (var kvp in _vectors)
        {
            var point = kvp.Value;
            var similarity = CalculateCosineSimilarity(queryVector, point.Vector);
            results.Add((point, similarity));
        }

        // Sort by similarity (descending) and take top results
        var topResults = results
            .OrderByDescending(r => r.score)
            .Take(limit)
            .Select(r => new SearchResult
            {
                Id = r.point.Id,
                Score = r.score,
                Metadata = r.point.Metadata
            })
            .ToList();

        _logger.LogDebug("Found {Count} similar points from {TotalCount} vectors", topResults.Count, _vectors.Count);
        return Task.FromResult(topResults);
    }

    public Task ClearAsync()
    {
        var count = _vectors.Count;
        _vectors.Clear();
        _logger.LogInformation("Cleared {Count} vectors from in-memory store", count);
        return Task.CompletedTask;
    }

    public Task<int> GetVectorCountAsync()
    {
        if (_useSqlite && _collection != null)
        {
            // For SqliteVec, return a placeholder count since exact count is not critical for initialization
            // The actual data is loaded automatically by SqliteVec
            _logger.LogInformation("SqliteVec database is active - vector count not available during initialization");
            return Task.FromResult(-1); // Indicates SqliteVec is active
        }

        // Fallback: return in-memory count
        return Task.FromResult(_vectors.Count);
    }

    public Task<bool> RemoveAsync(string id)
    {
        if (string.IsNullOrEmpty(id))
        {
            _logger.LogWarning("Cannot remove vector with empty ID");
            return Task.FromResult(false);
        }

        var removed = _vectors.TryRemove(id, out var removedPoint);

        if (removed)
        {
            _logger.LogDebug("Removed vector with ID: {Id}", id);
        }
        else
        {
            _logger.LogDebug("Vector with ID {Id} not found for removal", id);
        }

        return Task.FromResult(removed);
    }

    // SaveToDiskAsync removed - VectorStoreService is now read-only
    // Use the separate DatabaseConverter utility to create database.bin files

    /// <summary>
    /// Get all vectors for database conversion utility
    /// </summary>
    public Task<List<VectorPoint>> GetAllVectorsAsync()
    {
        return Task.FromResult(_vectors.Values.ToList());
    }

    /// <summary>
    /// Save vectors to database file (used by DatabaseConverter utility)
    /// </summary>
    public async Task SaveToDatabaseFileAsync(string filePath)
    {
        try
        {
            _logger.LogInformation("Saving {VectorCount} vectors to database file: {FilePath}", _vectors.Count, filePath);

            var vectorData = new VectorStoreData
            {
                VectorSize = _vectorSize,
                Vectors = _vectors.Values.ToList(),
                SavedAt = DateTime.UtcNow
            };

            var json = JsonSerializer.Serialize(vectorData, new JsonSerializerOptions
            {
                WriteIndented = false
            });

            var jsonBytes = System.Text.Encoding.UTF8.GetBytes(json);

            // Compress the data to save space
            using var fileStream = new FileStream(filePath, FileMode.Create, FileAccess.Write);
            using var gzipStream = new GZipStream(fileStream, CompressionLevel.Optimal);
            await gzipStream.WriteAsync(jsonBytes);

            _logger.LogInformation("Successfully saved database file. Size: {FileSize} bytes", new FileInfo(filePath).Length);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to save database file");
            throw;
        }
    }

    public async Task LoadFromDiskAsync()
    {
        if (!File.Exists(_persistenceFilePath))
        {
            _logger.LogInformation("No existing vector store found at {FilePath}. Starting with empty store.", _persistenceFilePath);
            return;
        }

        try
        {
            _logger.LogInformation("Loading vector store from disk: {FilePath}", _persistenceFilePath);

            using var fileStream = new FileStream(_persistenceFilePath, FileMode.Open, FileAccess.Read);
            using var gzipStream = new GZipStream(fileStream, CompressionMode.Decompress);
            using var memoryStream = new MemoryStream();

            await gzipStream.CopyToAsync(memoryStream);
            var jsonBytes = memoryStream.ToArray();
            var json = System.Text.Encoding.UTF8.GetString(jsonBytes);

            var vectorData = JsonSerializer.Deserialize<VectorStoreData>(json);

            if (vectorData == null)
            {
                throw new InvalidOperationException("Failed to deserialize vector store data");
            }

            _vectorSize = vectorData.VectorSize;
            _vectors.Clear();

            foreach (var vector in vectorData.Vectors)
            {
                _vectors.TryAdd(vector.Id, vector);
            }

            _logger.LogInformation("Successfully loaded {VectorCount} vectors from disk. Data saved at: {SavedAt}",
                _vectors.Count, vectorData.SavedAt);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to load vector store from disk");
            throw;
        }
    }

    private static float CalculateCosineSimilarity(float[] vector1, float[] vector2)
    {
        if (vector1.Length != vector2.Length)
        {
            throw new ArgumentException("Vectors must have the same length");
        }

        // Use System.Numerics.Tensors for optimized operations
        var span1 = vector1.AsSpan();
        var span2 = vector2.AsSpan();

        var dotProduct = TensorPrimitives.Dot(span1, span2);
        var magnitude1 = MathF.Sqrt(TensorPrimitives.Dot(span1, span1));
        var magnitude2 = MathF.Sqrt(TensorPrimitives.Dot(span2, span2));

        if (magnitude1 == 0 || magnitude2 == 0)
        {
            return 0;
        }

        return dotProduct / (magnitude1 * magnitude2);
    }
}

public class VectorPoint
{
    public string Id { get; set; } = string.Empty;
    public float[] Vector { get; set; } = Array.Empty<float>();
    public Dictionary<string, object> Metadata { get; set; } = new();
    public DateTime Timestamp { get; set; }
}

public class SearchResult
{
    public string Id { get; set; } = string.Empty;
    public float Score { get; set; }
    public Dictionary<string, object> Metadata { get; set; } = new();
}

public class VectorStoreData
{
    public int VectorSize { get; set; }
    public List<VectorPoint> Vectors { get; set; } = new();
    public DateTime SavedAt { get; set; }
}

// Data model for SQLite vector store - following Microsoft documentation
public class PowerShellCommand
{
    [VectorStoreKey]
    public string Id { get; set; } = string.Empty;

    [VectorStoreData(StorageName = "command_name")]
    public string CommandName { get; set; } = string.Empty;

    [VectorStoreData(StorageName = "description")]
    public string Description { get; set; } = string.Empty;

    [VectorStoreData(StorageName = "full_text")]
    public string FullText { get; set; } = string.Empty;

    [VectorStoreData(StorageName = "metadata_json")]
    public string MetadataJson { get; set; } = string.Empty;

    [VectorStoreData(StorageName = "timestamp")]
    public string Timestamp { get; set; } = string.Empty;

    [VectorStoreVector(Dimensions: 768, DistanceFunction = DistanceFunction.CosineDistance)]
    public ReadOnlyMemory<float> Vector { get; set; }
}

// Helper methods for VectorStoreService
public static class VectorStoreServiceHelpers
{
    public static string GetMetadataValue(Dictionary<string, object> metadata, string key, string defaultValue)
    {
        return metadata.TryGetValue(key, out var value) ? value?.ToString() ?? defaultValue : defaultValue;
    }
}
