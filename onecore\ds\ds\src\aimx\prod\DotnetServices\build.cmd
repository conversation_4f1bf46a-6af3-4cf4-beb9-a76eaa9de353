@echo off
echo Building NetRAG Solution...
echo.

echo Building NetRagService...
dotnet build NetRagService\NetRagService.csproj --configuration Release
if %ERRORLEVEL% neq 0 (
    echo ERROR: NetRagService build failed!
    exit /b 1
)

echo.
echo Building DatabaseConverter...
dotnet build DatabaseConverter\DatabaseConverter.csproj --configuration Release
if %ERRORLEVEL% neq 0 (
    echo ERROR: DatabaseConverter build failed!
    exit /b 1
)

echo.
echo Build completed successfully!
echo.
echo Output files:
echo   NetRagService.exe: NetRagService\bin\Release\net8.0\win-x64\NetRagService.exe
echo   DatabaseConverter.exe: DatabaseConverter\bin\Release\net8.0\win-x64\DatabaseConverter.exe
echo.
