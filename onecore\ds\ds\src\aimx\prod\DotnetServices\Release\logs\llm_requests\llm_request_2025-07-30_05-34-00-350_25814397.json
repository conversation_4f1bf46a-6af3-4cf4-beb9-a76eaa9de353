{"model": "Phi-4-mini-instruct-cuda-gpu", "messages": [{"role": "system", "content": "You are an IT expert. Answer concisely."}, {"role": "user", "content": "You are a PowerShell Active Directory expert. Generate a precise PowerShell command based on the user request.\n\n## CRITICAL RULES:\n\n1. **Extract the EXACT username/identifier from the user request** - do not use example names\n2. **MANDATORY Space Validation**: If identifier contains spaces → MUST use -Filter 'Name -like \"*identifier*\"'\n3. **Parameter Minimalism**: Only add parameters the user explicitly requested\n\n## Parameter Selection Logic:\n\n**ALWAYS use -Filter when identifier contains spaces:**\n- \"<PERSON>\" → Get-ADUser -Filter 'Name -like \"*<PERSON>*\"'\n- \"<PERSON>\" → Get-ADUser -Filter 'Name -like \"*<PERSON>*\"'\n- \"Service Account\" → Get-ADUser -Filter 'Name -like \"*Service Account*\"'\n\n**Use -Identity only when identifier has NO spaces:**\n- \"jdoe\" → Get-ADUser -Identity 'jdoe'\n- \"rizhang\" → Get-ADUser -Identity 'rizhang'\n- \"john.smith\" → Get-ADUser -Identity 'john.smith'\n\n## Examples of correct parameter extraction:\n- \"get user information for j<PERSON><PERSON>\" → Get-ADUser -Identity 'jdoe'\n- \"get user information for <PERSON>\" → Get-ADUser -Filter 'Name -like \"*<PERSON>*\"'\n- \"get user information for rizhang\" → Get-ADUser -Identity 'rizhang'\n- \"get user information for Linda Taylor\" → Get-ADUser -Filter 'Name -like \"*Linda Taylor*\"'\n\nCOMMAND CONTEXT:\n{\r\n  \"CommandName\": \"Get-ADDefaultDomainPasswordPolicy\",\r\n  \"Syntax\": [\r\n    \"Get-ADDefaultDomainPasswordPolicy\\n    [[-Current] ]\\n    [-AuthType ]\\n    [-Credential ]\\n    [-Server ]\\n    []\",\r\n    \"Get-ADDefaultDomainPasswordPolicy\\n    [-Identity] \\n    [-AuthType ]\\n    [-Credential ]\\n    [-Server ]\\n    []\"\r\n  ],\r\n  \"Parameters\": [\r\n    {\r\n      \"Name\": \"AuthType\",\r\n      \"Description\": \"The default authentication method is Negotiate. Negotiate or 0, Basic or 1\",\r\n      \"Details\": {\r\n        \"AcceptPipelineInput\": \"False\",\r\n        \"Required\": \"False\",\r\n        \"AcceptWildcardCharacters\": \"False\",\r\n        \"Type\": \"ADAuthType\",\r\n        \"Aliases\": \"\",\r\n        \"Default\": \"None\",\r\n        \"Position\": \"Named\"\r\n      }\r\n    },\r\n    {\r\n      \"Name\": \"Credential\",\r\n      \"Description\": \"If the acting credentials do not have directory-level permission to perform the task, Active Directory module for Windows PowerShell returns a terminating error.\",\r\n      \"Details\": {\r\n        \"AcceptPipelineInput\": \"False\",\r\n        \"Required\": \"False\",\r\n        \"AcceptWildcardCharacters\": \"False\",\r\n        \"Type\": \"PSCredential\",\r\n        \"Aliases\": \"\",\r\n        \"Default\": \"None\",\r\n        \"Position\": \"Named\"\r\n      }\r\n    },\r\n    {\r\n      \"Name\": \"Current\",\r\n      \"Description\": \"\",\r\n      \"Details\": {\r\n        \"AcceptPipelineInput\": \"False\",\r\n        \"Required\": \"False\",\r\n        \"AcceptWildcardCharacters\": \"False\",\r\n        \"Type\": \"ADCurrentDomainType\",\r\n        \"Aliases\": \"\",\r\n        \"Default\": \"None\",\r\n        \"Position\": \"1\"\r\n      }\r\n    },\r\n    {\r\n      \"Name\": \"Identity\",\r\n      \"Description\": \"This parameter can also get this object through the pipeline or you can set this parameter to a domain object instance. A distinguished name, A GUID (objectGUID), A security identifier (objectSid), A DNS domain name, A NetBIOS domain name\",\r\n      \"Details\": {\r\n        \"AcceptPipelineInput\": \"True\",\r\n        \"Required\": \"True\",\r\n        \"AcceptWildcardCharacters\": \"False\",\r\n        \"Type\": \"ADDefaultDomainPasswordPolicy\",\r\n        \"Aliases\": \"\",\r\n        \"Default\": \"None\",\r\n        \"Position\": \"0\"\r\n      }\r\n    },\r\n    {\r\n      \"Name\": \"Server\",\r\n      \"Description\": \"Specify the Active Directory Domain Services instance in one of the following ways: Fully qualified domain name, NetBIOS name Fully qualified directory server name, NetBIOS name, Fully qualified directory server name and port By using the Server value from objects passed through the pipeline, By using the server information associated with the Active Directory Domain Services Windows PowerShell provider drive, when the cmdlet runs in that drive, By using the domain of the computer running Windows PowerShell\",\r\n      \"Details\": {\r\n        \"AcceptPipelineInput\": \"False\",\r\n        \"Required\": \"False\",\r\n        \"AcceptWildcardCharacters\": \"False\",\r\n        \"Type\": \"String\",\r\n        \"Aliases\": \"\",\r\n        \"Default\": \"None\",\r\n        \"Position\": \"Named\"\r\n      }\r\n    },\r\n    {\r\n      \"Name\": \"CommonParameters\",\r\n      \"Description\": \"This cmdlet supports the common parameters: -Debug, -ErrorAction, -ErrorVariable,\\n-InformationAction, -InformationVariable, -OutBuffer, -OutVariable, -PipelineVariable,\\n-ProgressAction, -Verbose, -WarningAction, and -WarningVariable. For more information, see\\nabout_CommonParameters.\",\r\n      \"Details\": {\r\n        \"AcceptPipelineInput\": \"False\",\r\n        \"Required\": \"False\",\r\n        \"AcceptWildcardCharacters\": \"False\",\r\n        \"Type\": \"CommonParameters\",\r\n        \"Aliases\": \"\",\r\n        \"Default\": \"\",\r\n        \"Position\": \"Named\"\r\n      }\r\n    }\r\n  ],\r\n  \"Examples\": [\r\n    {\r\n      \"Title\": \"\",\r\n      \"Code\": \"PS C:\\\\\\u003E Get-ADDefaultDomainPasswordPolicy -Current LoggedOnUser\",\r\n      \"Description\": \"This command gets the default domain password policy from current logged on user domain.\"\r\n    },\r\n    {\r\n      \"Title\": \"\",\r\n      \"Code\": \"PS C:\\\\\\u003E Get-ADDefaultDomainPasswordPolicy -Current LocalComputer\",\r\n      \"Description\": \"This command gets the default domain password policy from current local computer.\"\r\n    },\r\n    {\r\n      \"Title\": \"\",\r\n      \"Code\": \"PS C:\\\\\\u003E Get-ADDefaultDomainPasswordPolicy -Identity fabrikam.com\",\r\n      \"Description\": \"This command gets the default domain password policy from the domain specified by the Site parameter.\"\r\n    },\r\n    {\r\n      \"Title\": \"\",\r\n      \"Code\": \"PS C:\\\\\\u003E (Get-ADForest -Current LoggedOnUser).Domains | %{ Get-ADDefaultDomainPasswordPolicy -Identity $_ }\",\r\n      \"Description\": \"This command gets the default domain password policy objects from all the domains in the forest.\"\r\n    },\r\n    {\r\n      \"Title\": \"\",\r\n      \"Code\": \"PS C:\\\\\\u003E Get-ADDefaultDomainPasswordPolicy\",\r\n      \"Description\": \"This command gets the default domain password policy from current logged on user domain.\"\r\n    }\r\n  ],\r\n  \"Description\": \"\",\r\n  \"Synopsis\": \"\",\r\n  \"Inputs\": [],\r\n  \"Outputs\": [],\r\n  \"Notes\": [],\r\n  \"RelatedLinks\": []\r\n}\n\nRICH COMMAND CONTEXT:\n{\r\n  \"CommandName\": \"Get-ADDefaultDomainPasswordPolicy\",\r\n  \"Syntax\": [\r\n    \"Get-ADDefaultDomainPasswordPolicy\\n    [[-Current] ]\\n    [-AuthType ]\\n    [-Credential ]\\n    [-Server ]\\n    []\",\r\n    \"Get-ADDefaultDomainPasswordPolicy\\n    [-Identity] \\n    [-AuthType ]\\n    [-Credential ]\\n    [-Server ]\\n    []\"\r\n  ],\r\n  \"Parameters\": [\r\n    {\r\n      \"Name\": \"AuthType\",\r\n      \"Description\": \"The default authentication method is Negotiate. Negotiate or 0, Basic or 1\",\r\n      \"Details\": {\r\n        \"AcceptPipelineInput\": \"False\",\r\n        \"Required\": \"False\",\r\n        \"AcceptWildcardCharacters\": \"False\",\r\n        \"Type\": \"ADAuthType\",\r\n        \"Aliases\": \"\",\r\n        \"Default\": \"None\",\r\n        \"Position\": \"Named\"\r\n      }\r\n    },\r\n    {\r\n      \"Name\": \"Credential\",\r\n      \"Description\": \"If the acting credentials do not have directory-level permission to perform the task, Active Directory module for Windows PowerShell returns a terminating error.\",\r\n      \"Details\": {\r\n        \"AcceptPipelineInput\": \"False\",\r\n        \"Required\": \"False\",\r\n        \"AcceptWildcardCharacters\": \"False\",\r\n        \"Type\": \"PSCredential\",\r\n        \"Aliases\": \"\",\r\n        \"Default\": \"None\",\r\n        \"Position\": \"Named\"\r\n      }\r\n    },\r\n    {\r\n      \"Name\": \"Current\",\r\n      \"Description\": \"\",\r\n      \"Details\": {\r\n        \"AcceptPipelineInput\": \"False\",\r\n        \"Required\": \"False\",\r\n        \"AcceptWildcardCharacters\": \"False\",\r\n        \"Type\": \"ADCurrentDomainType\",\r\n        \"Aliases\": \"\",\r\n        \"Default\": \"None\",\r\n        \"Position\": \"1\"\r\n      }\r\n    },\r\n    {\r\n      \"Name\": \"Identity\",\r\n      \"Description\": \"This parameter can also get this object through the pipeline or you can set this parameter to a domain object instance. A distinguished name, A GUID (objectGUID), A security identifier (objectSid), A DNS domain name, A NetBIOS domain name\",\r\n      \"Details\": {\r\n        \"AcceptPipelineInput\": \"True\",\r\n        \"Required\": \"True\",\r\n        \"AcceptWildcardCharacters\": \"False\",\r\n        \"Type\": \"ADDefaultDomainPasswordPolicy\",\r\n        \"Aliases\": \"\",\r\n        \"Default\": \"None\",\r\n        \"Position\": \"0\"\r\n      }\r\n    },\r\n    {\r\n      \"Name\": \"Server\",\r\n      \"Description\": \"Specify the Active Directory Domain Services instance in one of the following ways: Fully qualified domain name, NetBIOS name Fully qualified directory server name, NetBIOS name, Fully qualified directory server name and port By using the Server value from objects passed through the pipeline, By using the server information associated with the Active Directory Domain Services Windows PowerShell provider drive, when the cmdlet runs in that drive, By using the domain of the computer running Windows PowerShell\",\r\n      \"Details\": {\r\n        \"AcceptPipelineInput\": \"False\",\r\n        \"Required\": \"False\",\r\n        \"AcceptWildcardCharacters\": \"False\",\r\n        \"Type\": \"String\",\r\n        \"Aliases\": \"\",\r\n        \"Default\": \"None\",\r\n        \"Position\": \"Named\"\r\n      }\r\n    },\r\n    {\r\n      \"Name\": \"CommonParameters\",\r\n      \"Description\": \"This cmdlet supports the common parameters: -Debug, -ErrorAction, -ErrorVariable,\\n-InformationAction, -InformationVariable, -OutBuffer, -OutVariable, -PipelineVariable,\\n-ProgressAction, -Verbose, -WarningAction, and -WarningVariable. For more information, see\\nabout_CommonParameters.\",\r\n      \"Details\": {\r\n        \"AcceptPipelineInput\": \"False\",\r\n        \"Required\": \"False\",\r\n        \"AcceptWildcardCharacters\": \"False\",\r\n        \"Type\": \"CommonParameters\",\r\n        \"Aliases\": \"\",\r\n        \"Default\": \"\",\r\n        \"Position\": \"Named\"\r\n      }\r\n    }\r\n  ],\r\n  \"Examples\": [\r\n    {\r\n      \"Title\": \"\",\r\n      \"Code\": \"PS C:\\\\\\u003E Get-ADDefaultDomainPasswordPolicy -Current LoggedOnUser\",\r\n      \"Description\": \"This command gets the default domain password policy from current logged on user domain.\"\r\n    },\r\n    {\r\n      \"Title\": \"\",\r\n      \"Code\": \"PS C:\\\\\\u003E Get-ADDefaultDomainPasswordPolicy -Current LocalComputer\",\r\n      \"Description\": \"This command gets the default domain password policy from current local computer.\"\r\n    },\r\n    {\r\n      \"Title\": \"\",\r\n      \"Code\": \"PS C:\\\\\\u003E Get-ADDefaultDomainPasswordPolicy -Identity fabrikam.com\",\r\n      \"Description\": \"This command gets the default domain password policy from the domain specified by the Site parameter.\"\r\n    },\r\n    {\r\n      \"Title\": \"\",\r\n      \"Code\": \"PS C:\\\\\\u003E (Get-ADForest -Current LoggedOnUser).Domains | %{ Get-ADDefaultDomainPasswordPolicy -Identity $_ }\",\r\n      \"Description\": \"This command gets the default domain password policy objects from all the domains in the forest.\"\r\n    },\r\n    {\r\n      \"Title\": \"\",\r\n      \"Code\": \"PS C:\\\\\\u003E Get-ADDefaultDomainPasswordPolicy\",\r\n      \"Description\": \"This command gets the default domain password policy from current logged on user domain.\"\r\n    }\r\n  ],\r\n  \"Description\": \"\",\r\n  \"Synopsis\": \"\",\r\n  \"Inputs\": [],\r\n  \"Outputs\": [],\r\n  \"Notes\": [],\r\n  \"RelatedLinks\": []\r\n}\n\n**User Request:** \"Execute Get-ADDefaultDomainPasswordPolicy to retrieve domain password policy settings\"\n\nGenerate ONLY the PowerShell command - no explanations or additional text.\n\n**PowerShell Command:**\n\n\n\n"}], "max_tokens": 4096, "temperature": 0.7, "top_k": 40, "top_p": 0.9, "stream": false}