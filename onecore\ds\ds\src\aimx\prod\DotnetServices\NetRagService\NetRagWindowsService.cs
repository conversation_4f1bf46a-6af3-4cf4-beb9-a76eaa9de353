/*++

Copyright (c) Microsoft Corporation. All rights reserved.

Module Name:

    NetRagWindowsService.cs

Abstract:

    Windows Service implementation for the NetRag service.
    Handles service lifecycle and background processing.

Author:

    <PERSON><PERSON><PERSON> (rizhang) 07/29/2025

Environment:

    User mode only.

--*/

using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.SemanticKernel;
using System.Runtime.Versioning;

namespace NetRagService;

/// <summary>
/// Windows service host for the NetRag MCP Tools service
/// </summary>
public class NetRagWindowsService : BackgroundService
{
    private readonly ILogger<NetRagWindowsService> _logger;
    private readonly IServiceProvider _serviceProvider;
    private readonly RagConfiguration _configuration;
    private WebApplication? _webApp;

    public NetRagWindowsService(
        ILogger<NetRagWindowsService> logger,
        IServiceProvider serviceProvider,
        RagConfiguration configuration)
    {
        _logger = logger;
        _serviceProvider = serviceProvider;
        _configuration = configuration;
    }

    protected override async Task ExecuteAsync(CancellationToken stoppingToken)
    {
        try
        {
            _logger.LogInformation("NetRag Windows Service starting...");

            // Create and configure the web application
            var builder = WebApplication.CreateBuilder();

            // Configure the web application with the same configuration as the console app
            await ConfigureWebApplication(builder);

            _webApp = builder.Build();

            // Configure the HTTP request pipeline
            ConfigureRequestPipeline(_webApp);

            // Initialize services
            await InitializeServices(_webApp);

            _logger.LogInformation("NetRag service configured successfully");
            _logger.LogInformation("Service will be available at: http://{Host}:{Port}", 
                _configuration.McpService.Host, _configuration.McpService.Port);

            // Start the web application
            await _webApp.RunAsync(stoppingToken);
        }
        catch (OperationCanceledException)
        {
            _logger.LogInformation("NetRag Windows Service is stopping due to cancellation");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Fatal error in NetRag Windows Service");
            throw;
        }
    }

    private Task ConfigureWebApplication(WebApplicationBuilder builder)
    {
        // Configure registry-only configuration
        builder.Configuration.Sources.Clear();
        builder.Configuration.AddAimxServiceRegistry();

        // Use the shared service configuration method from Program.cs
        Program.ConfigureServices(builder, _configuration, isConsoleMode: false);

        // Configure web host
        builder.WebHost.UseUrls($"http://{_configuration.McpService.Host}:{_configuration.McpService.Port}");

        return Task.CompletedTask;
    }

    private void ConfigureRequestPipeline(WebApplication app)
    {
        // Use the shared request pipeline configuration method from Program.cs
        Program.ConfigureRequestPipeline(app, _configuration);
    }

    private async Task InitializeServices(WebApplication app)
    {
        // Use the shared service initialization method from Program.cs
        await Program.InitializeServices(app, _configuration, _logger);
    }

    public override async Task StopAsync(CancellationToken cancellationToken)
    {
        _logger.LogInformation("NetRag Windows Service is stopping...");

        if (_webApp != null)
        {
            await _webApp.StopAsync(cancellationToken);
            await _webApp.DisposeAsync();
        }

        await base.StopAsync(cancellationToken);
        _logger.LogInformation("NetRag Windows Service stopped");
    }
}
