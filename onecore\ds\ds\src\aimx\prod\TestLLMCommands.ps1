# Interactive PowerShell LLM Command Generator Test
# This script allows you to test the LLM's ability to generate PowerShell commands

param(
    [string]$LlmServiceUrl = "http://*************:5273/v1/chat/completions"
)

Write-Host "=== PowerShell LLM Command Generator Test ===" -ForegroundColor Green
Write-Host "This script will send your requests directly to the LLM service to generate PowerShell commands." -ForegroundColor Yellow
Write-Host "Type 'exit' to quit, 'clear' to clear screen" -ForegroundColor Cyan
Write-Host ""

function Send-LLMRequest {
    param(
        [string]$UserRequest,
        [string]$ServiceUrl
    )

    $prompt = @"
You are an expert-level PowerShell assistant.

Your job is to translate the user's natural language request into a single, syntactically valid PowerShell command that would run in a production Active Directory environment.

RULES:
1. Use -Identity for usernames, SAM account names, GUIDs, or email-style user principal names.
2. Use -Filter when the user provides a display name, full name, or partial name.
3. If the user does not provide a specific attribute, do not guess — only include explicitly mentioned parameters.
4. Do not add explanations or notes. Only return a single valid PowerShell command.

User Request: "$UserRequest"

PowerShell Command:
"@

    $requestBody = @{
        model = "Phi-4-mini-instruct-cuda-gpu"
        messages = @(
            @{
                role = "system"
                content = "You are an IT expert. Answer concisely."
            },
            @{
                role = "user"
                content = $prompt
            }
        )
        max_tokens = 1024
        temperature = 0.3
        top_k = 40
        top_p = 0.9
        stream = $false
    } | ConvertTo-Json -Depth 4

    try {
        Write-Host "Sending request to LLM..." -ForegroundColor Yellow

        $response = Invoke-RestMethod -Uri $ServiceUrl -Method Post -Body $requestBody -ContentType "application/json" -TimeoutSec 30

        if ($response -and $response.choices -and $response.choices.Count -gt 0 -and $response.choices[0].message.content) {
            $command = $response.choices[0].message.content

            # Normalize output
            $command = $command -replace '```[a-z]*', ''
            $command = $command -replace '```', ''
            $command = $command -replace 'PowerShell Command:', ''
            $command = $command.Trim() -replace '^\s*\n', ''

            Write-Host "Generated Command:" -ForegroundColor Green
            Write-Host $command -ForegroundColor White

            # Optional logging
            Add-Content -Path ".\llm_command_log.txt" -Value "[$(Get-Date)] Request: $UserRequest`nCommand: $command`n`n"

            # Copy to clipboard
            $copy = Read-Host "Copy to clipboard? (y/n)"
            if ($copy -eq 'y' -or $copy -eq 'Y') {
                $command | Set-Clipboard
                Write-Host "Command copied to clipboard!" -ForegroundColor Green
            }
        }
        else {
            Write-Host "No valid response from LLM service." -ForegroundColor Red
            if ($response) {
                Write-Host "Raw response: $($response | ConvertTo-Json -Depth 3)" -ForegroundColor Yellow
            }
        }
    }
    catch {
        Write-Host "Error calling LLM service: $($_.Exception.Message)" -ForegroundColor Red
        Write-Host "Make sure the LLM service is running at: $ServiceUrl" -ForegroundColor Yellow
    }
}

# Main interactive loop
while ($true) {
    Write-Host ""
    Write-Host "Enter your request (or 'exit' to quit):" -ForegroundColor Cyan
    $userInput = Read-Host "Request"

    if ($userInput -eq 'exit') {
        Write-Host "Goodbye!" -ForegroundColor Green
        break
    }

    if ($userInput -eq 'clear') {
        Clear-Host
        Write-Host "=== PowerShell LLM Command Generator Test ===" -ForegroundColor Green
        continue
    }

    if ([string]::IsNullOrWhiteSpace($userInput)) {
        Write-Host "Please enter a valid request." -ForegroundColor Yellow
        continue
    }

    Write-Host ""
    Write-Host "Processing request: '$userInput'" -ForegroundColor Magenta
    Write-Host "----------------------------------------" -ForegroundColor Gray

    Send-LLMRequest -UserRequest $userInput -ServiceUrl $LlmServiceUrl
}
