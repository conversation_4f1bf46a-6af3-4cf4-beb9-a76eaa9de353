# Interactive PowerShell LLM Command Generator Test
# This script allows you to test the LLM's ability to generate PowerShell commands

param(
    [string]$LlmServiceUrl = "http://localhost:5000/api/llm/chat"
)

Write-Host "=== PowerShell LLM Command Generator Test ===" -ForegroundColor Green
Write-Host "This script will send your requests directly to the LLM service to generate PowerShell commands." -ForegroundColor Yellow
Write-Host "Type 'exit' to quit, 'clear' to clear screen" -ForegroundColor Cyan
Write-Host ""

function Send-LLMRequest {
    param(
        [string]$UserRequest,
        [string]$ServiceUrl
    )
    
    $prompt = @"
You are a PowerShell expert. Generate a precise PowerShell command to satisfy the user request.

CRITICAL RULES:
1. Extract the EXACT username/identifier from the user request - do not use example names
2. For user searches with full names (like "John Doe"), use -Filter with Name attribute
3. For unique identifiers (like "jdoe"), use -Identity
4. Only include parameters the user explicitly requested
5. Generate ONLY the PowerShell command - no explanations

User Request: "$UserRequest"

PowerShell Command:
"@

    $requestBody = @{
        prompt = $prompt
        maxTokens = 150
        temperature = 0.1
    } | ConvertTo-Json -Depth 3

    try {
        Write-Host "Sending request to LLM..." -ForegroundColor Yellow
        
        $response = Invoke-RestMethod -Uri $ServiceUrl -Method Post -Body $requestBody -ContentType "application/json" -TimeoutSec 30
        
        if ($response -and $response.response) {
            $command = $response.response.Trim()
            # Clean up common formatting issues
            $command = $command -replace '```powershell', '' -replace '```', '' -replace 'PowerShell Command:', ''
            $command = $command.Trim()
            
            Write-Host "Generated Command:" -ForegroundColor Green
            Write-Host $command -ForegroundColor White
            
            # Ask if user wants to copy to clipboard
            $copy = Read-Host "Copy to clipboard? (y/n)"
            if ($copy -eq 'y' -or $copy -eq 'Y') {
                $command | Set-Clipboard
                Write-Host "Command copied to clipboard!" -ForegroundColor Green
            }
        } else {
            Write-Host "No response received from LLM service" -ForegroundColor Red
        }
    }
    catch {
        Write-Host "Error calling LLM service: $($_.Exception.Message)" -ForegroundColor Red
        Write-Host "Make sure the LLM service is running at: $ServiceUrl" -ForegroundColor Yellow
    }
}

# Main interactive loop
while ($true) {
    Write-Host ""
    Write-Host "Enter your request (or 'exit' to quit):" -ForegroundColor Cyan
    $userInput = Read-Host "Request"
    
    if ($userInput -eq 'exit') {
        Write-Host "Goodbye!" -ForegroundColor Green
        break
    }
    
    if ($userInput -eq 'clear') {
        Clear-Host
        Write-Host "=== PowerShell LLM Command Generator Test ===" -ForegroundColor Green
        continue
    }
    
    if ([string]::IsNullOrWhiteSpace($userInput)) {
        Write-Host "Please enter a valid request." -ForegroundColor Yellow
        continue
    }
    
    Write-Host ""
    Write-Host "Processing request: '$userInput'" -ForegroundColor Magenta
    Write-Host "----------------------------------------" -ForegroundColor Gray
    
    Send-LLMRequest -UserRequest $userInput -ServiceUrl $LlmServiceUrl
}
