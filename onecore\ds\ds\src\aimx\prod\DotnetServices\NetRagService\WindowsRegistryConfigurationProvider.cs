using Microsoft.Extensions.Configuration;
using Microsoft.Win32;
using System.Runtime.InteropServices;

namespace NetRagService;

/// <summary>
/// Configuration provider that reads from Windows Registry
/// </summary>
public class WindowsRegistryConfigurationProvider : ConfigurationProvider
{
    private readonly string _registryPath;
    private readonly string _keyPrefix;

    public WindowsRegistryConfigurationProvider(string registryPath, string keyPrefix = "")
    {
        _registryPath = registryPath;
        _keyPrefix = keyPrefix;
    }

    public override void Load()
    {
        // Only load registry configuration on Windows
        if (!RuntimeInformation.IsOSPlatform(OSPlatform.Windows))
        {
            return;
        }

        try
        {
            using var key = Registry.LocalMachine.OpenSubKey(_registryPath);
            if (key == null)
            {
                return;
            }

            // Read all values from the registry key
            foreach (var valueName in key.GetValueNames())
            {
                var value = key.GetValue(valueName);
                if (value != null)
                {
                    var configKey = string.IsNullOrEmpty(_keyPrefix) 
                        ? valueName 
                        : $"{_keyPrefix}:{valueName}";
                    
                    Data[configKey] = value.ToString() ?? string.Empty;
                }
            }
        }
        catch (Exception ex)
        {
            // Log error but don't fail - registry configuration is optional
            Console.WriteLine($"Warning: Failed to read registry configuration from {_registryPath}: {ex.Message}");
        }
    }
}

/// <summary>
/// Configuration source for Windows Registry
/// </summary>
public class WindowsRegistryConfigurationSource : IConfigurationSource
{
    public string RegistryPath { get; set; } = string.Empty;
    public string KeyPrefix { get; set; } = string.Empty;

    public IConfigurationProvider Build(IConfigurationBuilder builder)
    {
        return new WindowsRegistryConfigurationProvider(RegistryPath, KeyPrefix);
    }
}

/// <summary>
/// Extension methods for adding Windows Registry configuration
/// </summary>
public static class WindowsRegistryConfigurationExtensions
{
    /// <summary>
    /// Add Windows Registry as a configuration source
    /// </summary>
    /// <param name="builder">Configuration builder</param>
    /// <param name="registryPath">Registry path (e.g., "SYSTEM\\CurrentControlSet\\Services\\AIMXSrv\\Parameters")</param>
    /// <param name="keyPrefix">Optional prefix for configuration keys</param>
    /// <returns>Configuration builder</returns>
    public static IConfigurationBuilder AddWindowsRegistry(
        this IConfigurationBuilder builder,
        string registryPath,
        string keyPrefix = "")
    {
        return builder.Add(new WindowsRegistryConfigurationSource
        {
            RegistryPath = registryPath,
            KeyPrefix = keyPrefix
        });
    }

    /// <summary>
    /// Add AIMX service registry configuration - reads all settings from registry
    /// </summary>
    /// <param name="builder">Configuration builder</param>
    /// <returns>Configuration builder</returns>
    public static IConfigurationBuilder AddAimxServiceRegistry(this IConfigurationBuilder builder)
    {
        return builder.AddWindowsRegistry(
            "SYSTEM\\CurrentControlSet\\Services\\AIMXSrv\\Parameters",
            ""); // No prefix - read all keys directly
    }
}
