using System.ComponentModel.DataAnnotations;

/*++

Copyright (c) Microsoft Corporation. All rights reserved.

Module Name:

    IntentPlanningConfiguration.cs

Abstract:

    Configuration classes for the Intent Planning Service.
    Contains service, LLM, workflow planning, and external service configurations.

Author:

    <PERSON><PERSON><PERSON> (rizhang) 07/29/2025

Environment:

    User mode only.

--*/

using AimxShared.Constants;

namespace IntentPlanningService.Configuration;

/// <summary>
/// Configuration for the Intent Planning Service
/// </summary>
public class IntentPlanningConfiguration
{
    /// <summary>
    /// Service configuration
    /// </summary>
    public ServiceConfiguration Service { get; set; } = new();

    /// <summary>
    /// SLM (Small Language Model) configuration
    /// </summary>
    public SlmConfiguration Slm { get; set; } = new();

    /// <summary>
    /// External service endpoints
    /// </summary>
    public ExternalServices ExternalServices { get; set; } = new();

    /// <summary>
    /// Intent analysis configuration
    /// </summary>
    public IntentAnalysisConfiguration IntentAnalysis { get; set; } = new();

    /// <summary>
    /// Workflow planning configuration
    /// </summary>
    public WorkflowPlanningConfiguration WorkflowPlanning { get; set; } = new();

    /// <summary>
    /// Security and risk assessment configuration
    /// </summary>
    public SecurityConfiguration Security { get; set; } = new();
}

/// <summary>
/// Service-level configuration
/// </summary>
public class ServiceConfiguration
{
    [Required]
    public string Host { get; set; } = AimxConstants.ServiceEndpoints.LocalHost;

    [Range(1, 65535)]
    public int Port { get; set; } = AimxConstants.ServiceEndpoints.IntentPlanningServicePort;

    public string ServiceName { get; set; } = "IntentPlanningService";

    public bool EnableCors { get; set; } = true;

    public bool EnableSwagger { get; set; } = true;

    public int RequestTimeoutSeconds { get; set; } = 30;

    public int MaxConcurrentRequests { get; set; } = 100;
}

/// <summary>
/// SLM configuration for intent understanding
/// </summary>
public class SlmConfiguration
{
    [Required]
    public string ModelPath { get; set; } = "model.onnx";

    [Required]
    public string VocabPath { get; set; } = "vocab.txt";

    public int MaxTokens { get; set; } = 512;

    public float Temperature { get; set; } = 0.1f; // Low temperature for consistent results

    public int VectorSize { get; set; } = 384;

    public bool EnableCaching { get; set; } = true;

    public int CacheExpirationMinutes { get; set; } = 60;

    public double ConfidenceThreshold { get; set; } = 0.7; // Minimum confidence for SLM results
}

/// <summary>
/// External service endpoints configuration
/// </summary>
public class ExternalServices
{
    /// <summary>
    /// Universal Tool Manager service endpoint
    /// </summary>
    public ServiceEndpoint ToolManager { get; set; } = new()
    {
        BaseUrl = AimxConstants.ServiceEndpoints.ToolManagerServiceBaseUrl,
        TimeoutSeconds = AimxConstants.Timeouts.ToolManagerTimeoutSeconds,
        MaxRetries = AimxConstants.RetrySettings.ToolManagerMaxRetries
    };

    /// <summary>
    /// RAG service endpoint for knowledge retrieval
    /// </summary>
    public ServiceEndpoint NetRagService { get; set; } = new()
    {
        BaseUrl = AimxConstants.ServiceEndpoints.NetRagServiceBaseUrl,
        TimeoutSeconds = AimxConstants.Timeouts.NetRagServiceTimeoutSeconds,
        MaxRetries = AimxConstants.RetrySettings.NetRagServiceMaxRetries
    };

    /// <summary>
    /// Workflow Orchestration Engine endpoint
    /// </summary>
    public ServiceEndpoint WorkflowEngine { get; set; } = new()
    {
        BaseUrl = AimxConstants.ServiceEndpoints.WorkflowEngineServiceBaseUrl,
        TimeoutSeconds = AimxConstants.Timeouts.WorkflowEngineTimeoutSeconds,
        MaxRetries = AimxConstants.RetrySettings.WorkflowEngineMaxRetries
    };

    /// <summary>
    /// AIMX Server endpoint for user confirmation
    /// </summary>
    public ServiceEndpoint AimxServer { get; set; } = new()
    {
        BaseUrl = AimxConstants.ServiceEndpoints.AimxServerBaseUrl,
        TimeoutSeconds = AimxConstants.Timeouts.DefaultHttpTimeoutSeconds,
        MaxRetries = AimxConstants.RetrySettings.DefaultMaxRetries
    };
}

/// <summary>
/// Service endpoint configuration
/// </summary>
public class ServiceEndpoint
{
    [Required]
    public string BaseUrl { get; set; } = string.Empty;

    public int TimeoutSeconds { get; set; } = 30;

    public int MaxRetries { get; set; } = 3;

    public int RetryDelaySeconds { get; set; } = 2;

    public bool EnableCircuitBreaker { get; set; } = true;

    public int CircuitBreakerFailureThreshold { get; set; } = 5;

    public int CircuitBreakerTimeoutSeconds { get; set; } = 60;

    public Dictionary<string, string> Headers { get; set; } = new();
}

/// <summary>
/// Intent analysis configuration
/// </summary>
public class IntentAnalysisConfiguration
{
    public double MinConfidenceThreshold { get; set; } = 0.6;

    public bool EnableContextEnrichment { get; set; } = true;

    public int MaxContextItems { get; set; } = 10;
}

/// <summary>
/// Workflow planning configuration
/// </summary>
public class WorkflowPlanningConfiguration
{
    public double MinPlanningConfidence { get; set; } = 0.7;

    public int MaxWorkflowSteps { get; set; } = 20;

    public int DefaultStepTimeoutSeconds { get; set; } = 300; // 5 minutes

    public int MaxRetryAttempts { get; set; } = 3;

    public bool EnableParallelExecution { get; set; } = true;

    public bool EnableRollbackPlanning { get; set; } = true;

    public bool EnableAlternativeWorkflows { get; set; } = true;

    public int MaxAlternativeWorkflows { get; set; } = 3;


}

/// <summary>
/// Security configuration
/// </summary>
public class SecurityConfiguration
{
    public bool EnableRiskAssessment { get; set; } = true;

    public bool RequireApprovalForHighRisk { get; set; } = true;

    public bool RequireApprovalForCriticalRisk { get; set; } = true;

    public List<string> AutoApprovedOperations { get; set; } = new()
    {
        "get_user",
        "list_users",
        "get_group",
        "list_groups",
        "get_computer",
        "list_computers",
        "search_ad"
    };

    public List<string> AlwaysRequireApproval { get; set; } = new()
    {
        "delete_user",
        "delete_computer",
        "modify_domain_admin",
        "change_domain_policy"
    };

    public Dictionary<string, List<string>> PermissionRequirements { get; set; } = new()
    {
        { "user_management", new List<string> { "AD_USER_ADMIN" } },
        { "group_management", new List<string> { "AD_GROUP_ADMIN" } },
        { "computer_management", new List<string> { "AD_COMPUTER_ADMIN" } },
        { "security_operations", new List<string> { "AD_SECURITY_ADMIN" } },
        { "system_administration", new List<string> { "AD_SYSTEM_ADMIN" } }
    };
}
