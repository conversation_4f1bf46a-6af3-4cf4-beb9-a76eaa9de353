﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <OutputType>Exe</OutputType>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
    <AssemblyName>DatabaseConverter</AssemblyName>
    <RootNamespace>DatabaseConverter</RootNamespace>
    <RuntimeIdentifier>win-x64</RuntimeIdentifier>
    <SelfContained>false</SelfContained>
    <TargetOS>windows</TargetOS>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.SemanticKernel" Version="1.60.0" />
    <PackageReference Include="Microsoft.SemanticKernel.Connectors.Onnx" Version="1.60.0-alpha" />
    <PackageReference Include="Microsoft.Extensions.Hosting" Version="8.0.0" />
    <PackageReference Include="Microsoft.Extensions.Logging" Version="8.0.0" />
    <PackageReference Include="Microsoft.Extensions.Logging.Console" Version="8.0.0" />
    <PackageReference Include="Microsoft.SemanticKernel.Connectors.SqliteVec" Version="1.61.0-preview" />
    <PackageReference Include="System.Numerics.Tensors" Version="9.0.7" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\NetRagService\NetRagService.csproj" />
  </ItemGroup>

  <ItemGroup>
    <!-- Copy model files from root directory to data subfolder -->
    <None Update="..\model.onnx">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <Link>data\model.onnx</Link>
    </None>
    <None Update="..\vocab.txt">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <Link>data\vocab.txt</Link>
    </None>
    <!-- Ensure data directory exists -->
    <None Update="data\**">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
  </ItemGroup>

</Project>
