{"model": "Phi-4-mini-instruct-cuda-gpu", "messages": [{"role": "system", "content": "You are an IT expert. Answer concisely."}, {"role": "user", "content": "You are a PowerShell expert. Analyze if this user request can be fulfilled by a SINGLE PowerShell cmdlet.\n\nDIRECT COMMAND PATTERNS:\n- \"get default domain password policy\" → Get-ADDefaultDomainPasswordPolicy\n- \"show user john doe\" → Get-<PERSON><PERSON><PERSON>\n- \"get all users\" → Get-<PERSON><PERSON><PERSON>\n- \"list domain controllers\" → Get-<PERSON><PERSON><PERSON><PERSON><PERSON>ontroller\n- \"show group members of finance\" → Get-ADGroupMember\n- \"get computer info for PC123\" → Get-ADComputer\n- \"show domain info\" → Get-ADDomain\n- \"list all groups\" → Get-ADGroup\n- \"get forest info\" → Get-ADForest\n- \"show replication status\" → Get-ADReplicationFailure\n\nRULES:\n1. If the request can be fulfilled by ONE cmdlet with parameters, return the cmdlet name\n2. If it requires multiple steps or complex logic, return 'COMPLEX'\n3. If unclear or ambiguous, return 'UNCLEAR'\n\nUser Request: \"get user information for <PERSON>\"\n\nRespond with:\n- The exact PowerShell cmdlet name (e.g., 'Get-ADDefaultDomainPasswordPolicy')\n- 'COMPLEX' if multiple steps needed\n- 'UNCLEAR' if ambiguous\n\nResponse:"}], "max_tokens": 4096, "temperature": 0.7, "top_k": 40, "top_p": 0.9, "stream": false}