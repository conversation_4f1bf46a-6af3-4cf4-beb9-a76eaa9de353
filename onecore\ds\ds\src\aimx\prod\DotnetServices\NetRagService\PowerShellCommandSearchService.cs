/*++

Copyright (c) Microsoft Corporation. All rights reserved.

Module Name:

    PowerShellCommandSearchService.cs

Abstract:

    Service for searching PowerShell commands using vector similarity.
    Provides semantic search capabilities for command discovery.

Author:

    <PERSON><PERSON><PERSON> (rizhang) 07/29/2025

Environment:

    User mode only.

--*/

using Microsoft.Extensions.AI;
using Microsoft.Extensions.Logging;

namespace NetRagService;

public class PowerShellCommandSearchService
{
    private readonly IEmbeddingGenerator<string, Embedding<float>> _embeddingService;
    private readonly VectorStoreService _vectorStoreService;
    private readonly ILogger<PowerShellCommandSearchService> _logger;

    public PowerShellCommandSearchService(
        IEmbeddingGenerator<string, Embedding<float>> embeddingService,
        VectorStoreService vectorStoreService,
        ILogger<PowerShellCommandSearchService> logger)
    {
        _embeddingService = embeddingService;
        _vectorStoreService = vectorStoreService;
        _logger = logger;
    }

    public async Task<List<PowerShellCommandResult>> SearchAsync(string query, int limit = 5)
    {
        if (string.IsNullOrWhiteSpace(query))
        {
            return new List<PowerShellCommandResult>();
        }

        try
        {
            _logger.LogInformation("Searching for PowerShell commands with query: '{Query}', limit: {Limit}", query, limit);

            // Use string-based search for SqliteVec compatibility
            // Get more results initially to allow for usage-based re-ranking
            var initialLimit = Math.Max(limit * 3, 20);
            var searchResults = await _vectorStoreService.SearchAsync(query, initialLimit);

            // Convert to PowerShell command results - handle both old and new metadata formats
            var commandResults = searchResults.Select(result => new PowerShellCommandResult
            {
                // Try new format first, fallback to old format
                CommandName = GetMetadataValue(result.Metadata, "command_name") ??
                             GetMetadataValue(result.Metadata, "commandName") ?? "Unknown",
                Score = result.Score,
                FullText = GetMetadataValue(result.Metadata, "full_text") ??
                          GetMetadataValue(result.Metadata, "fullText") ?? "",
                ParameterCount = GetMetadataInt(result.Metadata, "parameter_count") ??
                                GetMetadataInt(result.Metadata, "parameterCount") ?? 0,
                ExampleCount = GetMetadataInt(result.Metadata, "example_count") ??
                              GetMetadataInt(result.Metadata, "exampleCount") ?? 0,
                ParameterNames = GetMetadataValue(result.Metadata, "parameter_names") ??
                                GetMetadataValue(result.Metadata, "parameterNames") ?? "",
                Id = result.Id,
                Metadata = result.Metadata
            }).ToList();

            // Apply usage-based ranking to boost frequently used commands
            var rankedResults = ApplyUsageBasedRanking(commandResults).Take(limit).ToList();

            _logger.LogInformation("Found {ResultCount} matching PowerShell commands (ranked by usage)", rankedResults.Count);

            return rankedResults;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to search PowerShell commands for query: {Query}", query);
            throw;
        }
    }





    // Helper methods to handle both old and new metadata formats
    private string? GetMetadataValue(Dictionary<string, object> metadata, string key)
    {
        return metadata.TryGetValue(key, out var value) ? value?.ToString() : null;
    }

    private int? GetMetadataInt(Dictionary<string, object> metadata, string key)
    {
        return metadata.TryGetValue(key, out var value) && int.TryParse(value?.ToString(), out var intValue)
            ? intValue : null;
    }



    /// <summary>
    /// Enhanced search that can leverage new RAG-optimized metadata
    /// </summary>
    public async Task<List<EnhancedPowerShellCommandResult>> SearchEnhancedAsync(
        string query,
        int limit = 5,
        string? category = null,
        string? verb = null,
        string? complexity = null)
    {
        if (string.IsNullOrWhiteSpace(query))
        {
            return new List<EnhancedPowerShellCommandResult>();
        }

        try
        {
            _logger.LogInformation("Enhanced search for PowerShell commands with query: '{Query}', limit: {Limit}", query, limit);

            // Generate embedding for the search query
            // Get more results initially for filtering
            var searchLimit = (category != null || verb != null || complexity != null) ? Math.Max(limit * 3, 50) : limit;

            // Use string-based search for SqliteVec compatibility
            var searchResults = await _vectorStoreService.SearchAsync(query, searchLimit);

            // Apply enhanced filters if specified
            if (!string.IsNullOrEmpty(category))
            {
                searchResults = searchResults.Where(r =>
                    GetMetadataValue(r.Metadata, "category")?.Contains(category, StringComparison.OrdinalIgnoreCase) == true).ToList();
            }

            if (!string.IsNullOrEmpty(verb))
            {
                searchResults = searchResults.Where(r =>
                    string.Equals(GetMetadataValue(r.Metadata, "verb"), verb, StringComparison.OrdinalIgnoreCase)).ToList();
            }

            if (!string.IsNullOrEmpty(complexity))
            {
                searchResults = searchResults.Where(r =>
                    string.Equals(GetMetadataValue(r.Metadata, "complexity"), complexity, StringComparison.OrdinalIgnoreCase)).ToList();
            }

            // Convert to enhanced command results
            var commandResults = searchResults.Take(limit).Select(result => new EnhancedPowerShellCommandResult
            {
                // Core information
                CommandName = GetMetadataValue(result.Metadata, "command_name") ??
                             GetMetadataValue(result.Metadata, "commandName") ?? "Unknown",
                Verb = GetMetadataValue(result.Metadata, "verb") ?? "",
                Noun = GetMetadataValue(result.Metadata, "noun") ?? "",
                Category = GetMetadataValue(result.Metadata, "category") ?? "",
                PrimaryPurpose = GetMetadataValue(result.Metadata, "primary_purpose") ?? "",
                Description = GetMetadataValue(result.Metadata, "description") ?? "",
                Score = result.Score,

                // RAG-specific fields
                RagDocument = GetMetadataValue(result.Metadata, "rag_document") ?? "",
                Keywords = DeserializeStringList(GetMetadataValue(result.Metadata, "keywords") ?? "[]"),

                // Parameter information
                RequiredParameters = DeserializeStringList(GetMetadataValue(result.Metadata, "required_parameters") ?? "[]"),
                OptionalParameters = DeserializeStringList(GetMetadataValue(result.Metadata, "optional_parameters") ?? "[]"),
                ParameterCount = GetMetadataInt(result.Metadata, "parameter_count") ??
                                GetMetadataInt(result.Metadata, "parameterCount") ?? 0,
                ParameterNames = GetMetadataValue(result.Metadata, "parameter_names") ??
                                GetMetadataValue(result.Metadata, "parameterNames") ?? "",

                // Example information
                ExampleCount = GetMetadataInt(result.Metadata, "example_count") ??
                              GetMetadataInt(result.Metadata, "exampleCount") ?? 0,

                // Search metadata
                Id = result.Id,
                FullText = GetMetadataValue(result.Metadata, "full_text") ??
                          GetMetadataValue(result.Metadata, "fullText") ?? "",
                Metadata = result.Metadata
            }).ToList();

            _logger.LogInformation("Found {ResultCount} matching PowerShell commands with enhanced search", commandResults.Count);

            return commandResults;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to perform enhanced search for query: {Query}", query);
            throw;
        }
    }

    private List<string> DeserializeStringList(string json)
    {
        try
        {
            return System.Text.Json.JsonSerializer.Deserialize<List<string>>(json) ?? new List<string>();
        }
        catch
        {
            return new List<string>();
        }
    }

    /// <summary>
    /// Apply usage-based ranking to boost frequently used commands
    /// </summary>
    private List<PowerShellCommandResult> ApplyUsageBasedRanking(List<PowerShellCommandResult> results)
    {
        return results.OrderBy(result =>
        {
            // Get usage data from metadata
            var usageFrequency = GetMetadataInt(result.Metadata, "usage_frequency") ?? 1;
            var usageCategory = GetMetadataValue(result.Metadata, "usage_category") ?? "moderate";

            // Calculate usage boost factor
            var categoryMultiplier = usageCategory.ToLower() switch
            {
                "very_common" => 0.1f,  // Lower score = higher ranking
                "common" => 0.2f,
                "moderate" => 0.3f,
                "rare" => 0.4f,
                _ => 0.3f
            };

            // Frequency boost (higher frequency = lower penalty)
            var frequencyMultiplier = Math.Max(0.1f, 1.0f / Math.Max(1, usageFrequency));

            // Combined ranking score (lower = better)
            // Base similarity score + usage penalty
            return result.Score + (categoryMultiplier * frequencyMultiplier);
        }).ToList();
    }
}

public class PowerShellCommandResult
{
    public string CommandName { get; set; } = string.Empty;
    public float Score { get; set; }
    public string FullText { get; set; } = string.Empty;
    public int ParameterCount { get; set; }
    public int ExampleCount { get; set; }
    public string ParameterNames { get; set; } = string.Empty;
    public string Id { get; set; } = string.Empty;
    public Dictionary<string, object> Metadata { get; set; } = new();
}

// EnhancedPowerShellCommandResult is defined in EnhancedPowerShellCommandSearchService.cs
