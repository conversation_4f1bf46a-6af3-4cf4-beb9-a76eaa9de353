{"format": 1, "restore": {"D:\\os\\src\\onecore\\ds\\ds\\src\\aimx\\prod\\DotnetServices\\DatabaseConverter\\DatabaseConverter.csproj": {}}, "projects": {"D:\\os\\src\\onecore\\ds\\ds\\src\\aimx\\prod\\DotnetServices\\DatabaseConverter\\DatabaseConverter.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\os\\src\\onecore\\ds\\ds\\src\\aimx\\prod\\DotnetServices\\DatabaseConverter\\DatabaseConverter.csproj", "projectName": "DatabaseConverter", "projectPath": "D:\\os\\src\\onecore\\ds\\ds\\src\\aimx\\prod\\DotnetServices\\DatabaseConverter\\DatabaseConverter.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\os\\src\\onecore\\ds\\ds\\src\\aimx\\prod\\DotnetServices\\DatabaseConverter\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {"D:\\os\\src\\onecore\\ds\\ds\\src\\aimx\\prod\\DotnetServices\\NetRagService\\NetRagService.csproj": {"projectPath": "D:\\os\\src\\onecore\\ds\\ds\\src\\aimx\\prod\\DotnetServices\\NetRagService\\NetRagService.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"Microsoft.Extensions.Hosting": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.Extensions.Logging": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.Extensions.Logging.Console": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.SemanticKernel": {"target": "Package", "version": "[1.60.0, )"}, "Microsoft.SemanticKernel.Connectors.Onnx": {"target": "Package", "version": "[1.60.0-alpha, )"}, "Microsoft.SemanticKernel.Connectors.SqliteVec": {"target": "Package", "version": "[1.61.0-preview, )"}, "System.Numerics.Tensors": {"target": "Package", "version": "[9.0.7, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.302/PortableRuntimeIdentifierGraph.json"}}, "runtimes": {"win-x64": {"#import": []}}}, "D:\\os\\src\\onecore\\ds\\ds\\src\\aimx\\prod\\DotnetServices\\NetRagService\\NetRagService.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\os\\src\\onecore\\ds\\ds\\src\\aimx\\prod\\DotnetServices\\NetRagService\\NetRagService.csproj", "projectName": "NetRagService", "projectPath": "D:\\os\\src\\onecore\\ds\\ds\\src\\aimx\\prod\\DotnetServices\\NetRagService\\NetRagService.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\os\\src\\onecore\\ds\\ds\\src\\aimx\\prod\\DotnetServices\\NetRagService\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {"D:\\os\\src\\onecore\\ds\\ds\\src\\aimx\\prod\\DotnetServices\\Shared\\AimxShared.csproj": {"projectPath": "D:\\os\\src\\onecore\\ds\\ds\\src\\aimx\\prod\\DotnetServices\\Shared\\AimxShared.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"Microsoft.Extensions.Configuration": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.Extensions.Configuration.Json": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.Extensions.Hosting": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.Extensions.Hosting.WindowsServices": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.Extensions.Logging": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.Extensions.Logging.Console": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.Extensions.Logging.EventLog": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.PowerShell.SDK": {"target": "Package", "version": "[7.4.6, )"}, "Microsoft.SemanticKernel": {"target": "Package", "version": "[1.60.0, )"}, "Microsoft.SemanticKernel.Connectors.Onnx": {"target": "Package", "version": "[1.60.0-alpha, )"}, "Microsoft.SemanticKernel.Connectors.SqliteVec": {"target": "Package", "version": "[1.61.0-preview, )"}, "System.Numerics.Tensors": {"target": "Package", "version": "[9.0.7, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.AspNetCore.App": {"privateAssets": "none"}, "Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.302/PortableRuntimeIdentifierGraph.json"}}, "runtimes": {"win-x64": {"#import": []}}}, "D:\\os\\src\\onecore\\ds\\ds\\src\\aimx\\prod\\DotnetServices\\Shared\\AimxShared.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\os\\src\\onecore\\ds\\ds\\src\\aimx\\prod\\DotnetServices\\Shared\\AimxShared.csproj", "projectName": "AimxShared", "projectPath": "D:\\os\\src\\onecore\\ds\\ds\\src\\aimx\\prod\\DotnetServices\\Shared\\AimxShared.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\os\\src\\onecore\\ds\\ds\\src\\aimx\\prod\\DotnetServices\\Shared\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.302/PortableRuntimeIdentifierGraph.json"}}}}}