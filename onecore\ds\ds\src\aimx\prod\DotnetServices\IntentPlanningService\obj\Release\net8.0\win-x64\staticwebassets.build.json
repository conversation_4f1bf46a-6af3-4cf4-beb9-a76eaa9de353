{"Version": 1, "Hash": "s+Glzy23phmP7f/eH32FlswkcZKIm8QQpiqYkojlbOQ=", "Source": "IntentPlanningService", "BasePath": "_content/IntentPlanningService", "Mode": "<PERSON><PERSON><PERSON>", "ManifestType": "Build", "ReferencedProjectsConfiguration": [{"Identity": "D:\\os\\src\\onecore\\ds\\ds\\src\\aimx\\prod\\DotnetServices\\NetRagService\\NetRagService.csproj", "Version": 2, "Source": "NetRagService", "GetPublishAssetsTargets": "ComputeReferencedStaticWebAssetsPublishManifest;GetCurrentProjectPublishStaticWebAssetItems", "AdditionalPublishProperties": "Configuration=Release;Platform=AnyCPU", "AdditionalPublishPropertiesToRemove": "WebPublishProfileFile;TargetFramework", "GetBuildAssetsTargets": "GetCurrentProjectBuildStaticWebAssetItems", "AdditionalBuildProperties": "Configuration=Release;Platform=AnyCPU", "AdditionalBuildPropertiesToRemove": "WebPublishProfileFile;TargetFramework"}], "DiscoveryPatterns": [], "Assets": [], "Endpoints": []}