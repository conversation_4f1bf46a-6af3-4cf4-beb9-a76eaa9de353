/*++

Copyright (c) Microsoft Corporation. All rights reserved.

Module Name:

    RagConfiguration.cs

Abstract:

    Configuration classes for the RAG service.
    Contains settings for LLM services, vector stores, and PowerShell command processing.

Author:

    <PERSON><PERSON><PERSON> (rizhang) 07/29/2025

Environment:

    User mode only.

--*/

using AimxShared.Constants;

namespace NetRagService;

/// <summary>
/// Configuration for the RAG service - all values read from Windows Registry
/// Registry Location: HKLM\SYSTEM\CurrentControlSet\Services\AIMXSrv\Parameters
/// </summary>
public class RagConfiguration
{
    // Foundry Local Configuration
    public string FoundryLocalBaseUrl { get; set; } = AimxConstants.ServiceEndpoints.LlmServiceV1BaseUrl;
    public string FoundryLocalModelName { get; set; } = AimxConstants.ModelConfig.DefaultLlmModel;
    public string FoundryLocalServiceId { get; set; } = AimxConstants.ModelConfig.DefaultServiceId;

    // Embedding Configuration - use paths relative to executable location
    public string EmbeddingModelPath { get; set; } = GetExecutableRelativePath(AimxConstants.FilePaths.DefaultModelPath);
    public string EmbeddingVocabPath { get; set; } = GetExecutableRelativePath(AimxConstants.FilePaths.DefaultVocabPath);
    public int EmbeddingVectorSize { get; set; } = AimxConstants.ModelConfig.DefaultVectorSize;

    // Document Ingestion Configuration
    public int DocumentChunkSize { get; set; } = AimxConstants.SearchLimits.DefaultChunkSize;
    public int DocumentChunkOverlap { get; set; } = AimxConstants.SearchLimits.DefaultChunkOverlap;
    public int DocumentSearchLimit { get; set; } = AimxConstants.SearchLimits.DefaultSearchLimit;

    // Vector Store Configuration
    public int VectorStoreInitialCapacity { get; set; } = AimxConstants.SearchLimits.VectorStoreInitialCapacity;
    public bool VectorStoreEnableStatistics { get; set; } = AimxConstants.ServiceConfig.DefaultEnableStatistics;
    public string VectorStorePersistenceFilePath { get; set; } = GetExecutableRelativePath(AimxConstants.FilePaths.DefaultDatabasePath);

    // MCP Service Configuration
    public string McpServiceHost { get; set; } = AimxConstants.ServiceEndpoints.LocalHost;
    public int McpServicePort { get; set; } = AimxConstants.ServiceEndpoints.NetRagServicePort;
    public int McpServiceDefaultTopK { get; set; } = AimxConstants.SearchLimits.DefaultTopK;
    public int McpServiceMaxTopK { get; set; } = AimxConstants.SearchLimits.MaxTopK;
    public float McpServiceDefaultMinScore { get; set; } = AimxConstants.SearchLimits.DefaultMinScore;
    public bool McpServiceEnableCors { get; set; } = AimxConstants.ServiceConfig.DefaultEnableCors;

    // Backend Service Configuration
    public string BackendServiceUrl { get; set; } = AimxConstants.ServiceEndpoints.NetRagServiceBaseUrl;
    public int BackendServiceTimeout { get; set; } = AimxConstants.Timeouts.DefaultHttpTimeoutSeconds;



    // Helper properties for backward compatibility with existing code
    public FoundryLocalConfig FoundryLocal => new()
    {
        BaseUrl = FoundryLocalBaseUrl,
        ModelName = FoundryLocalModelName,
        ServiceId = FoundryLocalServiceId
    };

    public EmbeddingConfig Embedding => new()
    {
        ModelPath = EmbeddingModelPath,
        VocabPath = EmbeddingVocabPath,
        VectorSize = EmbeddingVectorSize
    };

    public DocumentIngestionConfig DocumentIngestion => new()
    {
        ChunkSize = DocumentChunkSize,
        ChunkOverlap = DocumentChunkOverlap,
        SearchLimit = DocumentSearchLimit
    };

    public InMemoryVectorStoreConfig InMemoryVectorStore => new()
    {
        InitialCapacity = VectorStoreInitialCapacity,
        EnableStatistics = VectorStoreEnableStatistics,
        PersistenceFilePath = VectorStorePersistenceFilePath
    };

    public McpServiceConfig McpService => new()
    {
        Host = McpServiceHost,
        Port = McpServicePort,
        DefaultTopK = McpServiceDefaultTopK,
        MaxTopK = McpServiceMaxTopK,
        DefaultMinScore = McpServiceDefaultMinScore,
        EnableCors = McpServiceEnableCors,
        BackendServiceUrl = BackendServiceUrl,
        BackendServiceTimeout = BackendServiceTimeout
    };



    /// <summary>
    /// Helper method to get file paths relative to the executable location
    /// </summary>
    /// <param name="fileName">The file name to resolve</param>
    /// <returns>Full path relative to the executable directory</returns>
    private static string GetExecutableRelativePath(string fileName)
    {
        // Get the directory where the executable is located
        var executablePath = System.Reflection.Assembly.GetExecutingAssembly().Location;
        var executableDirectory = Path.GetDirectoryName(executablePath);

        if (string.IsNullOrEmpty(executableDirectory))
        {
            // Fallback to current directory if we can't determine executable location
            return Path.Combine(".", fileName);
        }

        return Path.Combine(executableDirectory, fileName);
    }
}

public class FoundryLocalConfig
{
    public string BaseUrl { get; set; } = AimxConstants.ServiceEndpoints.LlmServiceV1BaseUrl;
    public string ModelName { get; set; } = AimxConstants.ModelConfig.AlternateLlmModel;
    public string ServiceId { get; set; } = AimxConstants.ModelConfig.AlternateServiceId;
}

public class InMemoryVectorStoreConfig
{
    public int InitialCapacity { get; set; } = AimxConstants.SearchLimits.VectorStoreInitialCapacity;
    public bool EnableStatistics { get; set; } = AimxConstants.ServiceConfig.DefaultEnableStatistics;
    public string PersistenceFilePath { get; set; } = AimxConstants.FilePaths.DefaultDatabasePath;
}

public class EmbeddingConfig
{
    public string ModelPath { get; set; } = AimxConstants.FilePaths.JinaModelPath;
    public string VocabPath { get; set; } = AimxConstants.FilePaths.JinaVocabPath;
    public int VectorSize { get; set; } = AimxConstants.ModelConfig.DefaultVectorSize;
}

public class DocumentIngestionConfig
{
    public int ChunkSize { get; set; } = AimxConstants.SearchLimits.DefaultChunkSize;
    public int ChunkOverlap { get; set; } = AimxConstants.SearchLimits.DefaultChunkOverlap;
    public int SearchLimit { get; set; } = AimxConstants.SearchLimits.DefaultSearchLimit;
}

public class McpServiceConfig
{
    public string Host { get; set; } = AimxConstants.ServiceEndpoints.LocalHost;
    public int Port { get; set; } = AimxConstants.ServiceEndpoints.NetRagServicePort;
    public int DefaultTopK { get; set; } = AimxConstants.SearchLimits.DefaultTopK;
    public int MaxTopK { get; set; } = AimxConstants.SearchLimits.MaxTopK;
    public float DefaultMinScore { get; set; } = AimxConstants.SearchLimits.DefaultMinScore;
    public bool EnableCors { get; set; } = AimxConstants.ServiceConfig.DefaultEnableCors;

    // Registry-configurable properties (automatically bound from registry keys)
    public string BackendServiceUrl { get; set; } = AimxConstants.ServiceEndpoints.NetRagServiceBaseUrl;
    public int BackendServiceTimeout { get; set; } = AimxConstants.Timeouts.DefaultHttpTimeoutSeconds;
}


