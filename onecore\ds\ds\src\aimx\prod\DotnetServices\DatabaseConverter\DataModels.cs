/*++

Copyright (c) Microsoft Corporation. All rights reserved.

Module Name:

    DataModels.cs

Abstract:

    Data models for database conversion between legacy and enhanced formats.
    Contains structures for PowerShell command data transformation.

Author:

    <PERSON><PERSON><PERSON> (rizhang) 07/29/2025

Environment:

    User mode only.

--*/

using System.Text.Json;
using System.Text.Json.Serialization;

namespace DatabaseConverter;

// Original data structures for legacy JSON format
public class PowerShellCommandData
{
    public string? CommandName { get; set; }
    public string? Synopsis { get; set; }
    public string? Description { get; set; }
    public string? Module { get; set; }
    public List<ParameterData>? Parameters { get; set; }
    public List<ExampleData>? Examples { get; set; }
}

// Enhanced data structures for RAG-optimized JSON format
public class RagOptimizedData
{
    public RagMetadata? Metadata { get; set; }
    public List<RagOptimizedCommand> Commands { get; set; } = new();
}

public class RagMetadata
{
    public string? CreatedAt { get; set; }
    public string? SourceFile { get; set; }
    public int TotalCommands { get; set; }
    public string? OptimizationVersion { get; set; }
    public string? Description { get; set; }
    public List<string>? Features { get; set; }
}

public class RagOptimizedCommand
{
    [JsonPropertyName("command_name")]
    public string? CommandName { get; set; }

    public string? Verb { get; set; }
    public string? Noun { get; set; }
    public string? Module { get; set; }

    // Category can be either a string or an array of strings in the JSON
    private object? _category;
    public object? Category
    {
        get => _category;
        set => _category = value;
    }

    // Usage frequency for ranking boost
    [JsonPropertyName("usage_frequency")]
    public int UsageFrequency { get; set; } = 0;

    [JsonPropertyName("usage_category")]
    public string? UsageCategory { get; set; } // "very_common", "common", "moderate", "rare"

    // Helper property to get category as string
    public string CategoryAsString
    {
        get
        {
            if (_category is string str)
                return str;
            if (_category is JsonElement element)
            {
                if (element.ValueKind == JsonValueKind.String)
                    return element.GetString() ?? "";
                if (element.ValueKind == JsonValueKind.Array)
                    return string.Join(", ", element.EnumerateArray().Select(e => e.GetString() ?? ""));
            }
            if (_category is List<string> list)
                return string.Join(", ", list);
            return _category?.ToString() ?? "";
        }
    }

    public string? PrimaryPurpose { get; set; }
    public string? Description { get; set; }
    public List<string>? Keywords { get; set; }
    [JsonPropertyName("rag_document")]
    public string? RagDocument { get; set; }
    public List<string>? RequiredParameters { get; set; }
    public List<string>? OptionalParameters { get; set; }
    public List<ParameterData>? Parameters { get; set; }
    public List<ExampleData>? Examples { get; set; }
}

// Shared parameter and example data structures
public class ParameterData
{
    public string Name { get; set; } = "";
    public string Description { get; set; } = "";
    public string Type { get; set; } = "";
    public bool Mandatory { get; set; }
    public string Position { get; set; } = "";
    public bool PipelineInput { get; set; }
    
    // Legacy support
    public bool Required 
    { 
        get => Mandatory; 
        set => Mandatory = value; 
    }
}

public class ExampleData
{
    public string Title { get; set; } = "";
    public string Description { get; set; } = "";
    public string Code { get; set; } = "";
    public string Scenario { get; set; } = "";
}

// Vector store data structures (reused from the main service)
public class VectorStoreData
{
    public int VectorSize { get; set; }
    public List<VectorPoint> Vectors { get; set; } = new();
    public DateTime SavedAt { get; set; }
}

public class VectorPoint
{
    public string Id { get; set; } = string.Empty;
    public float[] Vector { get; set; } = Array.Empty<float>();
    public Dictionary<string, object> Metadata { get; set; } = new();
    public DateTime Timestamp { get; set; }
}
