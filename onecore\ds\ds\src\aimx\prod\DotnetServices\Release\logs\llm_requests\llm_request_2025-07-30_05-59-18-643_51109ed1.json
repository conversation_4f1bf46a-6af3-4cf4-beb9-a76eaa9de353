{"model": "Phi-4-mini-instruct-cuda-gpu", "messages": [{"role": "system", "content": "You are an IT expert. Answer concisely."}, {"role": "user", "content": "Generate a precise PowerShell command for this IT management task.\n\n**Command to generate:** Get-ADUser\n**User Request:** \"Execute [Get-ADUser] to check if user rizhang is locked out\"\n\n🔍 Context-Aware Parameter Selection:\n- If checking \"locked out\" status → add -Properties LockedOut\n- If checking \"enabled/disabled\" status → add -Properties Enabled\n- If checking \"expiration\" → add -Properties AccountExpirationDate\n- If checking \"password\" info → add -Properties PasswordLastSet, PasswordNeverExpires\n- For general \"user info\" → use default properties only\n\n🧩 Identifier Rules:\n- If identifier has no spaces → use -Identity 'identifier'\n- If identifier has spaces → use -Filter 'Name -like \"*identifier*\"'\n\n🛠 Examples:\n- \"check if user rizhang is locked\" → Get-ADUser -Identity rizhang -Properties LockedOut\n- \"get user info for <PERSON>\" → Get-ADUser -Filter 'Name -like \"*<PERSON>*\"'\n- \"show user rizhang details\" → Get-ADUser -Identity rizhang\n\nGenerate ONLY the PowerShell command:\n\n**PowerShell Command:**\n\n\n\n"}], "max_tokens": 4096, "temperature": 0.7, "top_k": 40, "top_p": 0.9, "stream": false}