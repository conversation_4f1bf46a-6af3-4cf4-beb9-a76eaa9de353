# AIMX Consolidated Release

Build Date: 2025-07-30 08:08:09
Configuration: Release

## Consolidated Deployment

All services and tools are in this single folder with shared dependencies.

## Available Executables

- IntentPlanningService.exe - Intent planning service (Port 5000)
- NetRagService.exe - RAG service (Port 8082)
- DatabaseConverter.exe - Database conversion tool

## Data Folder Structure

The data/ subfolder contains all model and database files:
- data/model.onnx - ONNX embedding model
- data/vocab.txt - Vocabulary file for tokenization
- data/database.bin - Vector database file
- data/comprehensive_ad_commands_dataset_final.json - Command dataset

## Usage

All executables can be run directly from this folder:

IntentPlanningService.exe
NetRagService.exe
DatabaseConverter.exe

## Setup Instructions

1. Place your model files in the data/ folder:
   - Copy model.onnx to data/model.onnx
   - Copy vocab.txt to data/vocab.txt
   - Copy database.bin to data/database.bin (if available)

2. Run the services directly from this folder

## Dependencies

All required DLLs and dependencies are consolidated in this folder.
Dependencies are shared between services to minimize disk usage.
No additional installation required.
