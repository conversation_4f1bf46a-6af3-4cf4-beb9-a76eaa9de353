#pragma once

#include <string>

/// <summary>
/// Centralized constants for AIMX C++ components to avoid hardcoded values.
/// These constants should match the values defined in AimxShared.Constants.AimxConstants
/// for consistency across C# and C++ components.
/// </summary>
namespace AimxConstants
{
    /// <summary>
    /// Service endpoints and URLs
    /// </summary>
    namespace ServiceEndpoints
    {
        // Local service hosts
        const std::wstring LocalHost = L"localhost";
        
        // NetRag Service
        const std::wstring NetRagServiceBaseUrl = L"http://localhost:5000";
        const int NetRagServicePort = 5000;
        
        // Intent Planning Service
        const std::wstring IntentPlanningServiceBaseUrl = L"http://localhost:8082";
        const int IntentPlanningServicePort = 8082;
        
        // Tool Manager Service
        const std::wstring ToolManagerServiceBaseUrl = L"http://localhost:8083";
        const int ToolManagerServicePort = 8083;
        
        // Workflow Engine Service
        const std::wstring WorkflowEngineServiceBaseUrl = L"http://localhost:8084";
        const int WorkflowEngineServicePort = 8084;
        
        // AIMX Server
        const std::wstring AimxServerBaseUrl = L"http://localhost:8080";
        const int AimxServerPort = 8080;
        
        // LLM Service (Foundry Local)
        const std::wstring LlmServiceBaseUrl = L"http://*************:5273";
        const std::wstring LlmServiceV1BaseUrl = L"http://localhost:5273/v1";
        const int LlmServicePort = 5273;
    }
    
    /// <summary>
    /// File paths and directories
    /// </summary>
    namespace FilePaths
    {
        // System paths
        const std::wstring ProgramDataAimxPath = L"C:\\ProgramData\\Microsoft\\aimx";
        const std::wstring ModelsJinaPath = L"C:\\ProgramData\\Microsoft\\aimx\\Models\\jina";
        const std::wstring NetRagServicePath = L"C:\\ProgramData\\Microsoft\\AIMX\\NetRagService\\NetRagService.exe";
        
        // Model files
        const std::wstring JinaModelPath = L"C:\\ProgramData\\Microsoft\\aimx\\Models\\jina\\model.onnx";
        const std::wstring JinaVocabPath = L"C:\\ProgramData\\Microsoft\\aimx\\Models\\jina\\vocab.txt";
        
        // Default file names
        const std::wstring DefaultModelPath = L"model.onnx";
        const std::wstring DefaultVocabPath = L"vocab.txt";
        const std::wstring DefaultDatabasePath = L"database.bin";
        
        // Data files
        const std::wstring ComprehensiveDatasetPath = L"data\\comprehensive_ad_commands_dataset_final.json";
    }
    
    /// <summary>
    /// Timeout values in seconds
    /// </summary>
    namespace Timeouts
    {
        // HTTP client timeouts
        const int DefaultHttpTimeoutSeconds = 30;
        const int ShortHttpTimeoutSeconds = 10;
        const int LongHttpTimeoutSeconds = 60;
        
        // Service-specific timeouts
        const int NetRagServiceTimeoutSeconds = 15;
        const int LlmServiceTimeoutSeconds = 30;
        const int ToolManagerTimeoutSeconds = 30;
        const int WorkflowEngineTimeoutSeconds = 60;
        
        // Request processing timeouts
        const int RequestTimeoutSeconds = 30;
        const int AnalysisTimeoutSeconds = 45;
        
        // Service startup/shutdown timeouts (in milliseconds)
        const int ServiceStartupTimeoutMs = 30000;
        const int ServiceShutdownTimeoutMs = 10000;
        const int HealthCheckTimeoutMs = 5000;
    }
    
    /// <summary>
    /// Service configuration constants
    /// </summary>
    namespace ServiceConfig
    {
        // Service names
        const std::wstring IntentPlanningServiceName = L"IntentPlanningService";
        const std::wstring NetRagServiceName = L"NetRagService";
        const std::wstring McpToolsServiceName = L"MCP Tools RAG Service";
        
        // Version information
        const std::wstring DefaultServiceVersion = L"1.0.0";
        
        // Registry configuration
        const std::wstring ServiceRegistryArgs = L"--service";
        
        // Feature flags
        const bool DefaultEnableCors = true;
        const bool DefaultEnableSwagger = true;
        const bool DefaultEnableCaching = true;
        const bool DefaultEnableStatistics = true;
        const bool DefaultEnableRiskAssessment = true;
        
        // Concurrency settings
        const int DefaultMaxConcurrentRequests = 100;
    }
    
    /// <summary>
    /// Search and query limits
    /// </summary>
    namespace SearchLimits
    {
        // Default search limits
        const int DefaultSearchLimit = 5;
        const int DefaultTopK = 10;
        const int MaxTopK = 100;
        const int MaxSearchLimit = 20;
        
        // RAG-specific limits
        const int RagSearchTopResults = 3;
        const float DefaultMinScore = 0.0f;
        
        // Document processing
        const int DefaultChunkSize = 300;
        const int DefaultChunkOverlap = 60;
        
        // Cache settings
        const int DefaultCacheCapacity = 1000;
        const int VectorStoreInitialCapacity = 10000;
        const int CacheExpirationMinutes = 60;
    }
    
    /// <summary>
    /// Model and AI configuration
    /// </summary>
    namespace ModelConfig
    {
        // LLM Model names
        const std::wstring DefaultLlmModel = L"Phi-3.5-mini-instruct-cuda-gpu";
        const std::wstring AlternateLlmModel = L"qwen2.5-0.5b-instruct-generic-gpu";
        
        // Service IDs
        const std::wstring DefaultServiceId = L"phi-3-mini";
        const std::wstring AlternateServiceId = L"qwen2.5-0.5b";
        
        // Token limits
        const int DefaultMaxTokens = 512;
        const int LargeMaxTokens = 4096;
        const int SmallMaxTokens = 100;
        
        // Temperature settings
        const float DefaultTemperature = 0.1f;
        const float HighTemperature = 0.7f;
        
        // Vector and embedding settings
        const int DefaultVectorSize = 768;
        const int AlternateVectorSize = 384;
        
        // Confidence thresholds
        const double DefaultConfidenceThreshold = 0.7;
        const double HighConfidenceThreshold = 0.95;
        const double MinConfidenceThreshold = 0.1;
    }
    
    /// <summary>
    /// Retry configuration
    /// </summary>
    namespace RetrySettings
    {
        const int DefaultMaxRetries = 3;
        const int NetRagServiceMaxRetries = 2;
        const int LlmServiceMaxRetries = 2;
        const int ToolManagerMaxRetries = 3;
        const int WorkflowEngineMaxRetries = 1;
        
        const int DefaultRetryDelaySeconds = 2;
        const int LongRetryDelaySeconds = 5;
    }
    
    /// <summary>
    /// Circuit breaker configuration
    /// </summary>
    namespace CircuitBreaker
    {
        const bool DefaultEnableCircuitBreaker = true;
        const int DefaultFailureThreshold = 5;
        const int DefaultTimeoutSeconds = 60;
        
        // Service-specific circuit breaker settings
        const int NetRagServiceFailureThreshold = 5;
        const int NetRagServiceTimeoutSeconds = 60;
        
        const int LlmServiceFailureThreshold = 3;
        const int LlmServiceTimeoutSeconds = 30;
        
        const int WorkflowEngineFailureThreshold = 2;
        const int WorkflowEngineTimeoutSeconds = 120;
    }
}
